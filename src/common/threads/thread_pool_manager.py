from PySide6.QtCore import QThreadPool, QRunnable, QObject, Signal

class WorkerSignals(QObject):
    result = Signal(object)
    error = Signal(Exception)

class WorkerRunnable(QRunnable):
    def __init__(self, target, callback=None, args=()):
        super().__init__()
        self._target = target
        self._args = args
        self.signals = WorkerSignals()
        if callback:
            self.signals.result.connect(callback)

    def run(self):
        try:
            result = self._target(*self._args)
            self.signals.result.emit(result)
        except Exception as e:
            self.signals.error.emit(e)

class ThreadPoolManager:
    _instance = None

    def __init__(self):
        self._pools = {}

    @staticmethod
    def instance():
        if not ThreadPoolManager._instance:
            ThreadPoolManager._instance = ThreadPoolManager()
        return ThreadPoolManager._instance

    def create_pool(self, name, max_threads = None):
        if name in self._pools:
            return self._pools[name]

        pool = QThreadPool()
        if max_threads:
            pool.setMaxThreadCount(max_threads)
        self._pools[name] = pool
        print(f"[ThreadPoolManager] Created pool '{name}' with max threads = {pool.maxThreadCount()}")
        return pool

    def run(self, pool_name, target, callback=None, args=()):
        print(f'pool_name = {pool_name}, target = {target}')
        pool = self._pools.get(pool_name)
        if pool:
            runnable = WorkerRunnable(target, callback, args)
            pool.start(runnable)
        else:
            raise ValueError(f"Pool '{pool_name}' not found!")

    def wait_for_done(self, pool_name=None):
        if "__internal_wait_pool__" not in self._pools:
            self.create_pool("__internal_wait_pool__", max_threads=1)

        def _wait(pool, name):
            pool.waitForDone()
            print(f"[ThreadPoolManager] Close pool '{name}' done.")

        if pool_name:
            pool = self._pools.get(pool_name)
            if pool:
                self.run("__internal_wait_pool__", target=_wait, args=(pool, pool_name))
        else:
            for name, pool in self._pools.items():
                pool.waitForDone()
                print(f"[ThreadPoolManager] Close pool '{name}' done.")
                # if name == "__internal_wait_pool__":
                #     continue  # Không tự wait chính nó!
                # self.run("__internal_wait_pool__", target=_wait, args=(pool, name))
        print(f"wait_for_done")
    def clear_all(self):
        self._pools.clear()

threadPoolManager = ThreadPoolManager.instance()