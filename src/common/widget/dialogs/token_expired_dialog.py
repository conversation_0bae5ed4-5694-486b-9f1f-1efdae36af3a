from src.common.widget.dialogs.base_dialog import NewBaseDialog, FooterType
from PySide6.QtWidgets import <PERSON><PERSON>abel, QWidget, QVBoxLayout

class TokenExpiredDialog(NewBaseDialog):
    def __init__(self, parent=None):
        content = QWidget()
        layout = QVBoxLayout()
        label = QLabel("Phiên đăng nhập của bạn đã hết hạn. Vui lòng đăng nhập lại.")
        label.setWordWrap(True)
        layout.addWidget(label)
        content.setLayout(layout)
        super().__init__(
            parent=parent,
            title="<PERSON>ên đăng nhập hết hạn",
            content_widget=content,
            footer_type=FooterType.CLOSE,
            width_dialog=400,
            min_height_dialog=150
        ) 