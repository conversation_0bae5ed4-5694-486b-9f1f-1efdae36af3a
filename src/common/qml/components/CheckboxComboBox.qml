import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

ComboBox {
    id: comboBox

    property var allItems: []
    property var allText: "All"
    property var selectedItems: allItems
    property bool isAllSelected: false
    property color _backgroundColor: "white"
    property color _borderColor: "white"
    property color _primaryColor: "white"
    property color _textColor: "white"
    property string _comboboxIcon: "white"

    textRole: "name"
    signal itemChangedSignal(var selectedItems)

    // Update allItems when model changes
    onModelChanged: {
        if (model) {
            allItems = []
            for (let i = 0; i < model.length; i++) {
                allItems.push(model[i])
            }
        }
    }

    displayText: {
        if (selectedItems.length === 0) {
            isAllSelected = false
            return "Select items..."
        } else if (selectedItems.length === allItems.length && allItems.length > 0) {
            isAllSelected = true
            return allText
        } else {
            isAllSelected = false
            return selectedItems.map(item => item.name).join(", ")
        }
    }

    background: Rectangle {
        color: _backgroundColor
        implicitWidth: 140
        implicitHeight: 32
        border.color: _borderColor
        border.width: 1
        radius: 10
    }
    contentItem: Item {
        id: clipper
        anchors {
            left: parent.left;    leftMargin: 8
            right: parent.right;  rightMargin: quality_icon_indicator.width + 8
            verticalCenter: parent.verticalCenter
        }
        clip: true

        Text {
            text: comboBox.displayText
            color: _textColor
            anchors.fill: parent
            font.pixelSize: 12
            wrapMode: Text.NoWrap
            elide: Text.ElideRight
            horizontalAlignment: Text.AlignLeft
            verticalAlignment: Text.AlignVCenter
        }
    }
    delegate: ItemDelegate {
        width: comboBox.width
        height: 32
        hoverEnabled: true

        property bool isAllItem: modelData && modelData.id === "all"
        property var currentItem: isAllItem ? null : modelData

        function toggleSelection() {
            if (isAllItem) {
                // Toggle all items
                if (selectedItems.length === allItems.length) {
                    // Deselect all
                    selectedItems = []
                } else {
                    // Select all
                    selectedItems = [...allItems]
                }
            } else {
                // Toggle individual item
                let itemExists = selectedItems.some(item => item.id === currentItem.id)
                if (itemExists) {
                    selectedItems = selectedItems.filter(item => item.id !== currentItem.id)
                } else {
                    selectedItems = [...selectedItems, currentItem]
                }
            }
            itemChangedSignal(selectedItems)
        }

        onClicked: {
            toggleSelection()
        }

        MouseArea {
            id: tooltipHoverArea
            anchors.fill: parent
            hoverEnabled: true
            propagateComposedEvents: true
            onClicked: (mouse) => {
                mouse.accepted = false
                parent.toggleSelection()
            }
            onPressed: (mouse) => mouse.accepted = false
            onReleased: (mouse) => mouse.accepted = false

            ToolTip {
                visible: tooltipHoverArea.containsMouse && !isAllItem
                delay: 300
                contentItem: Text {
                    text: currentItem ? currentItem.name : ""
                    color: "white"
                    wrapMode: Text.Wrap
                    width: tooltipHoverArea.width
                    font.pixelSize: 12
                }
                background: Rectangle {
                    color: "#0F1123"
                    radius: 5
                }
            }
        }
        contentItem: Item {
            anchors.fill: parent

            RowLayout {
                anchors.fill: parent
                anchors.leftMargin: 8
                anchors.rightMargin: 8
                anchors.topMargin: 4
                anchors.bottomMargin: 4
                spacing: 8

                CheckBox {
                    id: checkBox
                    Layout.alignment: Qt.AlignVCenter
                    checked: {
                        if (isAllItem) {
                            return selectedItems.length === allItems.length && allItems.length > 0
                        } else {
                            return selectedItems.some(item => item.id === currentItem.id)
                        }
                    }
                    onClicked: {
                        parent.parent.parent.toggleSelection()
                    }
                }

                Text {
                    text: isAllItem ? allText : (currentItem ? currentItem.name : "")
                    color: tooltipHoverArea.containsMouse ? _primaryColor : _textColor
                    font.pixelSize: 12
                    Layout.fillWidth: true
                    Layout.alignment: Qt.AlignVCenter
                    verticalAlignment: Text.AlignVCenter
                    horizontalAlignment: Text.AlignLeft
                    elide: Text.ElideRight
                    wrapMode: Text.NoWrap
                }
            }
        }
        background: Rectangle {
            color: _backgroundColor
            border.color: tooltipHoverArea.containsMouse ? _primaryColor : "transparent"
            border.width: 1
        }
    }

    indicator: Image {
        id: quality_icon_indicator
        source: _comboboxIcon
        anchors.verticalCenter: parent.verticalCenter
        anchors.right: parent.right
        anchors.rightMargin: 8
    }

    popup: Popup {
        y: comboBox.height - 1
        width: comboBox.width
        height: Math.min(contentItem.implicitHeight, comboBox.Window.height - topMargin - bottomMargin)
        padding: 1

        contentItem: ListView {
            clip: true
            implicitHeight: contentHeight
            model: {
                if (comboBox.popup.visible && comboBox.model) {
                    // Create a new model with "All" item at the beginning
                    let newModel = [{id: "all", name: allText}]
                    for (let i = 0; i < comboBox.model.length; i++) {
                        newModel.push(comboBox.model[i])
                    }
                    return newModel
                }
                return null
            }
            currentIndex: comboBox.highlightedIndex
            delegate: comboBox.delegate
        }

        background: Rectangle {
            color: _backgroundColor
            border.color: _borderColor
            radius: 4
        }
    }
}
