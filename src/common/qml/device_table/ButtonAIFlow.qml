import QtQuick
import QtQuick.Controls
import QtQuick.Layouts


Item {
    id: root
    implicitWidth: buttonLoader.implicitWidth
    height: 30
    property int aiType: 0
    property string buttonType: "Camera"

    readonly property int buttonRadius: 20
    property bool checked: aiType === 2
    signal clicked(str: string)
    signal checkboxClicked(flag: bool)

    Loader {
        id: buttonLoader
        anchors.fill: parent
        sourceComponent: buttonType === "Camera" ? idCamera : idGroup
    }
    Component {
        id: idCamera
        Rectangle {
            id: rectangle
            implicitWidth: rowLayout.implicitWidth + 20
            height: parent.height
            anchors.fill: parent
            border.width: 1.3
            border.color: getButtonColor(2)
            radius: buttonRadius
            color: getButtonColor(1)
            
            MouseArea {
                anchors.fill: parent
                hoverEnabled: true  

                onClicked: {
                    // root.checked = !root.checked
                    console.log("ButtonAIFlow checkbox clicked = ")
                    // scaleAnimator.running = true
                    // colorAnimator.running = true
                    // rectangle.color = getBackgroundColor(2)
                    root.clicked(ai_text)
                }
            }

            RowLayout {
                id: rowLayout
                spacing: 8
                anchors {
                    verticalCenter: parent.verticalCenter
                    left: parent.left
                    right: parent.right
                    leftMargin: 10
                    rightMargin: 10
                }

                Item {
                    id: idCheckbox
                    width: 16
                    height: 16
                    Layout.alignment: Qt.AlignVCenter
                    
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            root.checkboxClicked(root.checked)
                            checkboxAnimation.running = true
                        }
                    }

                    Image {
                        id: checkBoxIcon
                        anchors.fill: parent
                        source: root.checked ? checked_icon_path : unchecked_icon_path
                        sourceSize.width: 16
                        sourceSize.height: 16
                        opacity: 1.0
                        rotation: 0
                        smooth: true
                        antialiasing: true

                        SequentialAnimation {
                            id: checkboxAnimation
                            running: false

                            ParallelAnimation {
                                PropertyAnimation {
                                    target: checkBoxIcon
                                    property: "scale"
                                    from: 1.0
                                    to: 1.2
                                    duration: 300
                                    easing.type: Easing.OutCubic
                                }
                                PropertyAnimation {
                                    target: checkBoxIcon
                                    property: "opacity"
                                    from: 1.0
                                    to: 0.9
                                    duration: 300
                                    easing.type: Easing.OutCubic
                                }
                                PropertyAnimation {
                                    target: checkBoxIcon
                                    property: "rotation"
                                    from: 0
                                    to: 5
                                    duration: 300
                                    easing.type: Easing.OutCubic
                                }
                            }

                            ParallelAnimation {
                                PropertyAnimation {
                                    target: checkBoxIcon
                                    property: "scale"
                                    from: 1.2
                                    to: 1.0
                                    duration: 300
                                    easing.type: Easing.InOutCubic
                                }
                                PropertyAnimation {
                                    target: checkBoxIcon
                                    property: "opacity"
                                    from: 0.9
                                    to: 1.0
                                    duration: 300
                                    easing.type: Easing.InOutCubic
                                }
                                PropertyAnimation {
                                    target: checkBoxIcon
                                    property: "rotation"
                                    from: 5
                                    to: 0
                                    duration: 300
                                    easing.type: Easing.InOutCubic
                                }
                            }
                        }
                    }
                }

                CustomText {
                    text: ai_text
                    // Layout.fillWidth: true
                    Layout.alignment: Qt.AlignVCenter
                    defaultColor: getButtonColor(0)
                    font.pixelSize: 12
                    elide: Text.ElideRight
                }
            }
        }
    }

    Component {
        id: idGroup
        Rectangle {
            anchors.fill: parent
            implicitWidth: rowLayout.implicitWidth + 20
            height: parent.height
            radius: buttonRadius
            border.width: 1.3
            border.color: getButtonColor(2)
            color: getButtonColor(1)

            RowLayout {
                id: rowLayout
                spacing: 8
                anchors {
                    verticalCenter: parent.verticalCenter
                    left: parent.left
                    right: parent.right
                    leftMargin: 10
                    rightMargin: 10
                }

                CustomText {
                    text: ai_text
                    // Layout.fillWidth: true
                    Layout.alignment: Qt.AlignVCenter
                    horizontalAlignment: Text.AlignHCenter
                    defaultColor: getButtonColor(0)
                    font.pixelSize: 12
                    elide: Text.ElideRight
                }
            }

            MouseArea {
                anchors.fill: parent
                hoverEnabled: true
                onClicked: {
                    console.log("idGroup")
                    // root.checked = !root.checked
                    root.clicked(ai_text)
                }
            }
        }
    }
    // property int index: 0
    property string ai_text: "Default Button"
    property bool checkBoxChecked: false

    
    function getBasicButtonColor(component){ // aiType: (0: off, 1: on); component: (0: text, 1: background, 2: border)
        if(aiType === 0 || aiType === 1){
            if(component === 0){
                return Qt.rgba(textColor.r, textColor.g, textColor.b, 0.5)
            }
            else if (component === 1){
                return "transparent"
            }
            else if (component === 2){
                return Qt.rgba(textColor.r, textColor.g, textColor.b, 0.5)
            }
        }
        else if (aiType === 1){
            if(component === 0){
                return primaryColor
            }
            else if (component === 1){
                return Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.2)
            }
            else if (component === 2){
                return primaryColor
            }
        }
        return error_color
    }

    function getCheckboxButtonColor(component){ // aiType: (0: disable, 1: off, 2: on); component: (0: text, 1: background, 2: border)
        if(aiType === 0){
            if(component === 0){
                return Qt.rgba(textColor.r, textColor.g, textColor.b, 0.4)
            }
            else if (component === 1){
                return "transparent"
            }
            else if (component === 2){
                return primaryColor
            }
        }
        else if (aiType === 1){
            if(component === 0){
                return Qt.rgba(textColor.r, textColor.g, textColor.b, 0.5)
            }
            else if (component === 1){
                return "transparent"
            }
            else if (component === 2){
                return Qt.rgba(textColor.r, textColor.g, textColor.b, 0.5)
            }
        }
        else if (aiType === 2){
            if(component === 0){
                return textColor
            }
            else if (component === 1){
                return Qt.rgba(textColor.r, textColor.g, textColor.b, 0.2)
            }
            else if (component === 2){
                return primaryColor
            }
        }
        return error_color
    }

    function getButtonColor(component){ // component: (0: text, 1: background)
        if (buttonType === "Camera"){
            return getCheckboxButtonColor(component)
        }
        else{
            return getBasicButtonColor(component)
        }
    }

    
    
    readonly property color error_color: "#DE2C2C"
    property color backgroundColor: device_controller ? device_controller.get_color_theme_by_key("main_background") : "white"
    property color primaryColor: device_controller ? device_controller.get_color_theme_by_key("primary") : "white"
    property color textColor: device_controller ? device_controller.get_color_theme_by_key("text_color_all_app") : "white"

    Connections{
        target: device_controller
        function onThemeChanged() {
            backgroundColor = device_controller.get_color_theme_by_key("main_background")
            primaryColor = device_controller.get_color_theme_by_key("primary")
            textColor = device_controller.get_color_theme_by_key("text_color_all_app")
        }
    }

    readonly property string checked_icon_path: device_controller ? device_controller.get_image_theme_by_key("checkbox_ver2_checked") : ""
    readonly property string unchecked_icon_path: device_controller ? device_controller.get_image_theme_by_key("checkbox_ver2_unchecked") : ""
}
