from PySide6.QtCore import Qt, Signal, Slot
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QCheckBox, QLabel,
                               QPushButton, QComboBox, QSpacerItem,
                               QSizePolicy, QFrame)
from PySide6.QtGui import QPixmap, QPainter, QPainterPath, QCursor, QColor, QPen, QBrush
import logging
from src.common.controller.main_controller import main_controller
from src.styles.style import Style, Theme
from src.utils.setting_screen_qsetting import SettingScreenQSettings
from src.utils.theme_setting import theme_setting
from src.utils.utils import Utils
from src.presentation.setting_screen.widget.base_setting_tab import BaseSettingTab
import resources_rc
logger = logging.getLogger(__name__)

class GeneralSettingTab(BaseSettingTab):
    """Tab for general application settings including language and theme"""

    # Signals
    language_change_signal = Signal(object)
    theme_change_signal = Signal(Theme)

    def __init__(self, parent=None):
        """Initialize the general settings tab

        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        self.load_ui()
        self.set_dynamic_stylesheet()

    def load_ui(self):
        """Initialize and setup the UI components"""
        # 1. Interface theme section
        self.setup_interface_theme_section()
        self.add_separator()

        # 2. Data protection section
        self.setup_data_protection_section()
        self.add_separator()

        # 3. User activity section
        self.setup_user_activity_section()

        # Add spacer at the bottom
        self.add_bottom_spacer()

    def add_separator(self):
        """Add a horizontal separator line"""
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setFixedHeight(1)
        self.main_layout.addWidget(separator)

    def setup_interface_theme_section(self):
        """Setup interface theme section with checkboxes"""
        # Use BaseSettingTab's add_section method for consistent styling
        section_layout, labels_layout, controls_layout = self.create_section_layout(
            self.tr("Interface theme"),
            self.tr("Choose your style or customize your theme")
        )

        # Theme options layout
        theme_layout = QHBoxLayout()

        # Create checkboxes that behave like radio buttons
        current_theme = theme_setting.get_theme_color_from_qsetting()

        self.dark_mode_checkbox = self.create_theme_checkbox(
            self.tr("Dark mode"),
            "DARK",
            current_theme == "DARK"
        )
        self.dark_mode_checkbox.setObjectName("dark_mode_checkbox")

        self.light_mode_checkbox = self.create_theme_checkbox(
            self.tr("Light mode"),
            "LIGHT",
            current_theme == "LIGHT"
        )
        self.light_mode_checkbox.setObjectName("light_mode_checkbox")

        self.system_theme_checkbox = self.create_theme_checkbox(
            self.tr("System theme"),
            "AUTO",
            current_theme == "AUTO"
        )
        self.system_theme_checkbox.setObjectName("system_theme_checkbox")

        theme_layout.addWidget(self.dark_mode_checkbox)
        theme_layout.addWidget(self.light_mode_checkbox)
        theme_layout.addWidget(self.system_theme_checkbox)
        theme_layout.addStretch()

        # Add theme layout to controls layout (right side)
        controls_layout.addLayout(theme_layout)

        # Add to main layout
        self.main_layout.addLayout(section_layout)

        # Add language section
        self.setup_language_section()

    def setup_language_section(self):
        """Setup language selection section"""
        # Create horizontal layout for the entire section
        section_layout = QHBoxLayout()

        # Left side: Labels
        labels_layout = QVBoxLayout()
        labels_layout.setSpacing(4)

        title_label = QLabel(self.tr("Language"))
        title_label.setObjectName("language_title_label")
        title_label.setStyleSheet("QLabel { font-weight: bold; font-size: 16px; background-color: transparent; }")

        subtitle_label = QLabel(self.tr("Choose your preferred language"))
        subtitle_label.setObjectName("language_subtitle_label")
        subtitle_label.setStyleSheet("QLabel { color: gray; font-size: 12px; background-color: transparent; }")

        labels_layout.addWidget(title_label)
        labels_layout.addWidget(subtitle_label)

        # Right side: Language options
        language_layout = QHBoxLayout()

        # Get current language (you may need to implement this)
        current_language = self.get_current_language()

        self.english_checkbox = self.create_language_checkbox(
            self.tr("English"),
            "EN",
            current_language == "EN"
        )
        self.english_checkbox.setObjectName("english_checkbox")

        self.russian_checkbox = self.create_language_checkbox(
            self.tr("Русский"),
            "RU",
            current_language == "RU"
        )
        self.russian_checkbox.setObjectName("russian_checkbox")

        language_layout.addWidget(self.english_checkbox)
        language_layout.addWidget(self.russian_checkbox)
        language_layout.addStretch()

        # Add both sides to section layout
        section_layout.addLayout(labels_layout)
        section_layout.addLayout(language_layout)
        section_layout.setStretch(0, 1)  # Labels take 1 part
        section_layout.setStretch(1, 2)  # Language options take 2 parts

        # Add to main layout
        self.main_layout.addLayout(section_layout)

    def create_theme_checkbox(self, title, theme_key, is_checked):
        """Create a theme option widget with preview and checkbox"""
        widget = ThemeOptionWidget(theme_key, self.theme_checkbox_clicked)
        widget.setObjectName("theme_widget")
        # Initial styling will be handled by ThemeOptionWidget
        widget.setFixedSize(150, 140)
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)

        # Preview image with theme icon
        preview = QLabel()
        preview.setFixedWidth(130)

        # Get theme icon based on theme_key
        theme_icon = self.get_theme_icon(theme_key)

        preview.setStyleSheet(f"""
            QLabel {{
                border-radius: 8px;
                background-color: transparent;
                margin-bottom: 10px;
            }}
        """)


        # Create round pixmap
        round_pixmap = self.create_round_pixmap(theme_icon, preview.width())
        preview.setPixmap(round_pixmap)
        preview.setAlignment(Qt.AlignCenter)

        # Checkbox with title (behaves like radio button)
        checkbox = QCheckBox(title)
        checkbox.setChecked(is_checked)
        # Remove click handler since widget handles clicks now
        checkbox.setAttribute(Qt.WA_TransparentForMouseEvents, True)

        # Store checkbox reference in widget for translation access
        widget.checkbox = checkbox

        layout.addWidget(preview)
        layout.addWidget(checkbox)

        return widget

    def create_round_pixmap(self, icon_path, width):
        """Create a rounded rectangle pixmap from icon path"""
        # Load original pixmap
        original_pixmap = QPixmap(icon_path)
        if original_pixmap.isNull():
            logger.warning(f"Failed to load icon: {icon_path}")
            return QPixmap()

        # Scale pixmap to desired size
        scaled_pixmap = original_pixmap.scaledToWidth(width, Qt.SmoothTransformation)

        # Create rounded rectangle mask
        round_pixmap = QPixmap(scaled_pixmap.size())
        round_pixmap.fill(Qt.transparent)

        painter = QPainter(round_pixmap)
        painter.setRenderHint(QPainter.Antialiasing)

        # Create rounded rectangle path with 8px radius
        path = QPainterPath()
        path.addRoundedRect(0, 0, scaled_pixmap.width(), scaled_pixmap.height(), 8, 8)

        # Clip to rounded rectangle path and draw the scaled pixmap
        painter.setClipPath(path)
        painter.drawPixmap(0, 0, scaled_pixmap)
        painter.end()

        return round_pixmap

    def get_theme_icon(self, theme_key):
        """Get the appropriate theme icon based on theme key"""
        if theme_key == "DARK":
            return main_controller.get_theme_attribute("Image", "dark_mode_theme")
        elif theme_key == "LIGHT":
            return main_controller.get_theme_attribute("Image", "light_mode_theme")
        elif theme_key == "AUTO":
            return main_controller.get_theme_attribute("Image", "system_mode_theme")
        else:
            return ""

    def setup_data_protection_section(self):
        """Setup data protection section with checkboxes"""
        # Use BaseSettingTab's add_section method for consistent styling
        controls_layout = self.add_section(
            self.tr("Data protection"),
            self.tr("Protect your data with encryption and watermarking")
        )

        # Add checkboxes to controls layout
        self.https_checkbox = QCheckBox(self.tr("Use only HTTPS to connect to cameras"))
        self.https_checkbox.setObjectName("https_checkbox")
        self.encryption_checkbox = QCheckBox(self.tr("Force servers to accept only encrypted connections"))
        self.encryption_checkbox.setObjectName("encryption_checkbox")
        self.encryption_checkbox.setChecked(True)  # Default checked as shown in image
        self.video_encryption_checkbox = QCheckBox(self.tr("Encrypt video traffic to desktop and mobile clients"))
        self.video_encryption_checkbox.setObjectName("video_encryption_checkbox")
        self.watermark_checkbox = QCheckBox(self.tr("Display watermark with username over video"))
        self.watermark_checkbox.setObjectName("watermark_checkbox")

        controls_layout.addWidget(self.https_checkbox)
        controls_layout.addWidget(self.encryption_checkbox)
        controls_layout.addWidget(self.video_encryption_checkbox)
        controls_layout.addWidget(self.watermark_checkbox)

    def setup_user_activity_section(self):
        """Setup user activity section with checkboxes and dropdown"""
        # Use BaseSettingTab's add_section method for consistent styling
        controls_layout = self.add_section(
            self.tr("User activity"),
            self.tr("Monitor and control user activity")
        )

        # Add checkboxes to controls layout
        self.audit_trail_checkbox = QCheckBox(self.tr("Enable audit trail"))
        self.audit_trail_checkbox.setObjectName("audit_trail_checkbox")
        self.session_duration_checkbox = QCheckBox(self.tr("Limit session duration"))
        self.session_duration_checkbox.setObjectName("session_duration_checkbox")
        self.session_duration_checkbox.setChecked(True)  # Default checked as shown in image

        controls_layout.addWidget(self.audit_trail_checkbox)
        controls_layout.addWidget(self.session_duration_checkbox)

        # Session duration controls
        session_layout = QHBoxLayout()
        to_widget = QLabel(self.tr("to"))
        to_widget.setObjectName("to_label")
        to_widget.setStyleSheet(f"QLabel {{ color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')}; background-color: transparent; }}")
        session_layout.addWidget(to_widget)

        self.duration_combo = QComboBox()
        self.duration_combo.addItems(["30", "60", "90", "120"])
        self.duration_combo.setCurrentText("30")
        self.duration_combo.setFixedWidth(80)
        self.apply_combobox_style(self.duration_combo)

        self.period_combo = QComboBox()
        self.period_combo.addItems([self.tr("Day"), self.tr("Hour"), self.tr("Minute")])
        self.period_combo.setCurrentText(self.tr("Day"))
        self.period_combo.setFixedWidth(100)
        self.apply_combobox_style(self.period_combo)

        session_layout.addWidget(self.duration_combo)
        session_layout.addWidget(self.period_combo)
        session_layout.addStretch()

        controls_layout.addLayout(session_layout)

        # More checkboxes
        self.display_servers_checkbox = QCheckBox(self.tr("Display servers in tree for non-power users"))
        self.display_servers_checkbox.setObjectName("display_servers_checkbox")
        self.display_servers_checkbox.setChecked(True)  # Default checked as shown in image
        self.archive_encryption_checkbox = QCheckBox(self.tr("Archive encryption"))
        self.archive_encryption_checkbox.setObjectName("archive_encryption_checkbox")
        self.archive_encryption_checkbox.setChecked(True)  # Default checked as shown in image

        controls_layout.addWidget(self.display_servers_checkbox)
        controls_layout.addWidget(self.archive_encryption_checkbox)

    ###########################
    @Slot(str)
    def theme_checkbox_clicked(self, theme_key):
        """Handle theme checkbox selection (only one can be selected)"""

        # Uncheck all checkboxes first
        self.dark_mode_checkbox.findChild(QCheckBox).setChecked(False)
        self.light_mode_checkbox.findChild(QCheckBox).setChecked(False)
        self.system_theme_checkbox.findChild(QCheckBox).setChecked(False)

        # Check the selected one and apply theme
        if theme_key == "DARK":
            self.dark_mode_checkbox.findChild(QCheckBox).setChecked(True)
            main_controller.change_theme(Theme.DARK)
        elif theme_key == "LIGHT":
            self.light_mode_checkbox.findChild(QCheckBox).setChecked(True)
            main_controller.change_theme(Theme.LIGHT)
        elif theme_key == "AUTO":
            self.system_theme_checkbox.findChild(QCheckBox).setChecked(True)
            device_theme = theme_setting.device_theme()
            main_controller.change_theme(device_theme)

        # Save setting
        theme_setting.set_theme_color_to_qsetting(theme_key)

    @Slot()
    def change_password_clicked(self):
        """Handle change password button click"""
        pass
    ###########################

    def translate_ui_general(self):
        """Update UI text translations using findChild with ObjectName approach"""
        from PySide6.QtCore import QCoreApplication
        from PySide6.QtWidgets import QLabel, QCheckBox


        # Translation mapping
        translations = {
            # Title and subtitle labels
            "interface_theme_title_label": u"Interface theme",
            "interface_theme_subtitle_label": u"Choose your style or customize your theme",
            "language_title_label": u"Language",
            "language_subtitle_label": u"Choose your preferred language",
            "data_protection_title_label": u"Data protection",
            "data_protection_subtitle_label": u"Protect your data with encryption and watermarking",
            "user_activity_title_label": u"User activity",
            "user_activity_subtitle_label": u"Monitor and control user activity",
            "to_label": u"to",

            # Theme checkboxes
            "dark_mode_checkbox": u"Dark mode",
            "light_mode_checkbox": u"Light mode",
            "system_theme_checkbox": u"System theme",

            # Language checkboxes
            "english_checkbox": u"English",
            "russian_checkbox": u"Russia",

            # Data Protection checkboxes
            "https_checkbox": u"Use only HTTPS to connect to cameras",
            "encryption_checkbox": u"Force servers to accept only encrypted connections",
            "video_encryption_checkbox": u"Encrypt video traffic to desktop and mobile clients",
            "watermark_checkbox": u"Display watermark with username over video",

            # User Activity checkboxes
            "audit_trail_checkbox": u"Enable audit trail",
            "session_duration_checkbox": u"Limit session duration",
            "display_servers_checkbox": u"Display servers in tree for non-power users",
            "archive_encryption_checkbox": u"Archive encryption",
        }

        # Translate all widgets using findChild with ObjectName
        for object_name, text_key in translations.items():
            widget = None

            # Try different widget types
            widget = (self.findChild(QLabel, object_name) or
                     self.findChild(QCheckBox, object_name))

            # Special handling for theme and language checkboxes (they are stored as instance variables)
            if not widget:
                # Check if it's a theme or language checkbox
                if object_name in ['dark_mode_checkbox', 'light_mode_checkbox', 'system_theme_checkbox']:
                    container_widget = getattr(self, object_name.replace('_checkbox', '_checkbox'), None)
                    if container_widget and hasattr(container_widget, 'checkbox'):
                        widget = container_widget.checkbox
                elif object_name in ['english_checkbox', 'russian_checkbox']:
                    container_widget = getattr(self, object_name.replace('_checkbox', '_checkbox'), None)
                    if container_widget and hasattr(container_widget, 'checkbox'):
                        widget = container_widget.checkbox

            if widget and hasattr(widget, 'setText'):
                translated_text = QCoreApplication.translate("GeneralSettingTab", text_key, None)
                widget.setText(translated_text)
                pass
            else:
                pass

        # Combo box items that need manual translation
        # Period combo box
        current_period_index = self.period_combo.currentIndex()
        self.period_combo.clear()
        self.period_combo.addItems([
            QCoreApplication.translate("GeneralSettingTab", u"Day", None),
            QCoreApplication.translate("GeneralSettingTab", u"Hour", None),
            QCoreApplication.translate("GeneralSettingTab", u"Minute", None)
        ])
        if current_period_index < self.period_combo.count():
            self.period_combo.setCurrentIndex(current_period_index)







    def set_dynamic_stylesheet(self):
        """Apply dynamic theme-based styling to the widget"""
        text_color = main_controller.get_theme_attribute("Color", "text_color_all_app")
        bg_color = main_controller.get_theme_attribute("Color", "main_background")
        primary_color = main_controller.get_theme_attribute("Color", "primary")
        border_color = main_controller.get_theme_attribute("Color", "border_color")
        checked_img = main_controller.get_theme_attribute("Image", "checkbox_ver2_checked_non_qml")
        unchecked_img = main_controller.get_theme_attribute("Image", "checkbox_ver2_unchecked_non_qml")

        # Re-apply combo box styling when theme changes
        if hasattr(self, 'duration_combo'):
            self.apply_combobox_style(self.duration_combo)
        if hasattr(self, 'period_combo'):
            self.apply_combobox_style(self.period_combo)

        self.setStyleSheet(f"""
            QWidget {{
                color: {text_color};
                background-color: {bg_color};
            }}

            QLabel {{
                color: {text_color};
                background-color: transparent;
                border: none;
            }}

            QCheckBox {{
                color: {text_color};
                background-color: transparent;
                spacing: 8px;
                font-size: 14px;
            }}

            QCheckBox:hover {{
                color: {primary_color};
            }}

            QCheckBox::indicator {{
                width: 18px;
                height: 18px;
            }}

            QCheckBox::indicator:checked {{
                image: url({checked_img});
                border: none;
                background-color: transparent;
            }}

            QCheckBox::indicator:unchecked {{
                image: url({unchecked_img});
                border: none;
                background-color: transparent;
            }}

            QCheckBox::indicator:unchecked:hover {{
                image: url({unchecked_img});
                border: none;
                background-color: transparent;
            }}

            QCheckBox::indicator:checked:hover {{
                image: url({checked_img});
                border: none;
                background-color: transparent;
            }}

            QPushButton {{
                background-color: {primary_color};
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 16px;
            }}

            QPushButton:hover {{
                background-color: {primary_color};
                opacity: 0.8;
            }}

            QComboBox {{
                background-color: {bg_color};
                color: {text_color};
                border: 1px solid {border_color};
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 14px;
            }}

            QFrame {{
                background-color: {border_color};
            }}
        """)

    def create_language_checkbox(self, title, language_key, is_checked):
        """Create a language option widget with flag and checkbox"""
        widget = LanguageOptionWidget(language_key, self.language_checkbox_clicked)
        widget.setObjectName("language_widget")
        widget.setFixedSize(150, 140)
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)

        # Preview image with flag icon
        preview = QLabel()
        preview.setFixedWidth(130)

        # Get flag icon based on language_key
        flag_icon = self.get_flag_icon(language_key)

        preview.setStyleSheet(f"""
            QLabel {{
                border-radius: 8px;
                background-color: transparent;
                margin-bottom: 10px;
            }}
        """)

        if flag_icon:
            pixmap = QPixmap(flag_icon)
            if not pixmap.isNull():
                # Scale pixmap to fit preview with proper aspect ratio
                scaled_pixmap = pixmap.scaled(130, 80, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                preview.setPixmap(scaled_pixmap)
                preview.setAlignment(Qt.AlignCenter)
            else:
                # Fallback to emoji if image fails to load
                flag_text = "🇺🇸" if language_key == "EN" else "🇷🇺" if language_key == "RU" else language_key
                preview.setText(flag_text)
                preview.setAlignment(Qt.AlignCenter)
                preview.setStyleSheet(preview.styleSheet() + "font-size: 48px;")
        else:
            # Fallback to emoji if no flag icon path
            flag_text = "🇺🇸" if language_key == "EN" else "🇷🇺" if language_key == "RU" else language_key
            preview.setText(flag_text)
            preview.setAlignment(Qt.AlignCenter)
            preview.setStyleSheet(preview.styleSheet() + "font-size: 48px;")

        layout.addWidget(preview)

        # Checkbox with title (behaves like radio button)
        checkbox = QCheckBox(title)
        checkbox.setChecked(is_checked)
        # Set WA_TransparentForMouseEvents so widget click is handled instead of checkbox
        checkbox.setAttribute(Qt.WA_TransparentForMouseEvents, True)
        checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 14px;
                font-weight: bold;
                background-color: transparent;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)

        # Don't connect checkbox signal - widget click will handle it

        # Store checkbox reference in widget for translation access
        widget.checkbox = checkbox

        layout.addWidget(checkbox)
        layout.addStretch()

        return widget

    def get_flag_icon(self, language_key):
        """Get flag icon path based on language key"""
        try:
            if language_key == "EN":
                flag_path = main_controller.get_theme_attribute("Image", "flag_united_kingdom")
                return flag_path
            elif language_key == "RU":
                flag_path = main_controller.get_theme_attribute("Image", "flag_russia")
                return flag_path
            else:
                return None
        except Exception as e:
            logger.warning(f"Error getting flag icon for {language_key}: {e}")
            return None

    def get_current_language(self):
        """Get current language setting"""
        current_language = SettingScreenQSettings.get_instance().get_current_language()

        # Convert Style.Language enum to string
        if current_language == Style.Language.en:
            return "EN"
        elif current_language == Style.Language.ru:
            return "RU"
        else:
            return "EN"  # Default to English

    @Slot(str)
    def language_checkbox_clicked(self, language_key):
        """Handle language checkbox selection (only one can be selected) - like theme logic"""

        # Uncheck all language checkboxes first
        self.english_checkbox.findChild(QCheckBox).setChecked(False)
        self.russian_checkbox.findChild(QCheckBox).setChecked(False)

        # Check the selected one and set new language
        current_language = SettingScreenQSettings.get_instance().get_current_language()
        new_language = current_language

        if language_key == "EN":
            self.english_checkbox.findChild(QCheckBox).setChecked(True)
            new_language = Style.Language.en
        elif language_key == "RU":
            self.russian_checkbox.findChild(QCheckBox).setChecked(True)
            new_language = Style.Language.ru

        # Emit language change signal and save to settings
        self.language_change_signal.emit(new_language)
        if new_language != current_language:
            SettingScreenQSettings.get_instance().set_current_language(new_language)
            logger.info(f"Language changed from {current_language} to {new_language}")






class LanguageOptionWidget(QWidget):
    """Custom widget for language options with hover and click functionality"""

    def __init__(self, language_key, click_callback, parent=None):
        super().__init__(parent)
        self.language_key = language_key
        self.click_callback = click_callback
        self.is_hovered = False
        self.setCursor(QCursor(Qt.PointingHandCursor))

    def enterEvent(self, event):
        """Handle mouse enter event"""
        self.is_hovered = True
        self.update()  # Trigger repaint
        super().enterEvent(event)

    def leaveEvent(self, event):
        """Handle mouse leave event"""
        self.is_hovered = False
        self.update()  # Trigger repaint
        super().leaveEvent(event)

    def mousePressEvent(self, event):
        """Handle mouse press event"""
        if event.button() == Qt.LeftButton:
            self.click_callback(self.language_key)
        super().mousePressEvent(event)

    def paintEvent(self, event):
        """Custom paint event to draw background and border"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        overlay_color = main_controller.get_theme_attribute("Color", "background_overlay")
        # Convert RGBA string to QColor using utility function
        bg_color = Utils.convert_rgba_string_to_qcolor(overlay_color)

        if self.is_hovered:
            border_color = QColor(main_controller.get_theme_attribute("Color", "primary"))
            border_width = 2
        else:
            border_color = QColor(main_controller.get_theme_attribute("Color", "border_color"))
            border_width = 1

        # Draw background with rounded corners
        painter.setBrush(QBrush(bg_color))
        painter.setPen(QPen(border_color, border_width))

        rect = self.rect().adjusted(border_width//2, border_width//2, -border_width//2, -border_width//2)
        painter.drawRoundedRect(rect, 8, 8)

        super().paintEvent(event)


class ThemeOptionWidget(QWidget):
    """Custom widget for theme options with hover and click functionality"""

    def __init__(self, theme_key, click_callback, parent=None):
        super().__init__(parent)
        self.theme_key = theme_key
        self.click_callback = click_callback
        self.is_hovered = False
        self.setCursor(QCursor(Qt.PointingHandCursor))

    def enterEvent(self, event):
        """Handle mouse enter event"""
        self.is_hovered = True
        self.update()  # Trigger repaint
        super().enterEvent(event)

    def leaveEvent(self, event):
        """Handle mouse leave event"""
        self.is_hovered = False
        self.update()  # Trigger repaint
        super().leaveEvent(event)

    def mousePressEvent(self, event):
        """Handle mouse press event"""
        if event.button() == Qt.LeftButton:
            self.click_callback(self.theme_key)
        super().mousePressEvent(event)



    def paintEvent(self, event):
        """Custom paint event to draw background and border"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        overlay_color = main_controller.get_theme_attribute("Color", "background_overlay")
        # Convert RGBA string to QColor using utility function
        bg_color = Utils.convert_rgba_string_to_qcolor(overlay_color)

        if self.is_hovered:
            border_color = QColor(main_controller.get_theme_attribute("Color", "primary"))
            border_width = 2
        else:
            border_color = QColor(main_controller.get_theme_attribute("Color", "border_color"))
            border_width = 1

        # Draw background with rounded corners
        painter.setBrush(QBrush(bg_color))
        painter.setPen(QPen(border_color, border_width))

        rect = self.rect().adjusted(border_width//2, border_width//2, -border_width//2, -border_width//2)
        painter.drawRoundedRect(rect, 8, 8)

        super().paintEvent(event)


