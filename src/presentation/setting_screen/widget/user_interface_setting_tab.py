from PySide6.QtCore import Qt, Signal, Slot
from PySide6.QtWidgets import (QCheckBox, QComboBox, QSlider, QSpinBox,
                               QHBoxLayout, QVBoxLayout)
import logging
from src.presentation.setting_screen.widget.base_setting_tab import BaseSettingTab
from src.utils.config import Config

logger = logging.getLogger(__name__)


class UserInterfaceSettingTab(BaseSettingTab):
    """User Interface settings tab for display and UI preferences"""

    def __init__(self, parent=None):
        """Initialize the user interface settings tab"""
        super().__init__(parent)
        self.setup_sections()

    def setup_sections(self):
        """Setup all UI sections"""
        # 1. Grid Display Settings
        self.setup_grid_display_section()
        self.add_separator()
        
        # 2. Video Display Settings
        self.setup_video_display_section()
        self.add_separator()
        
        # 3. Toolbar & UI Elements
        self.setup_toolbar_section()
        self.add_separator()
        
        # 4. Debug & Information Display
        self.setup_debug_section()
        self.add_separator()
        
        # 5. Notification Settings
        self.setup_notification_section()

        # Add spacer at the bottom
        self.add_bottom_spacer()

    def setup_grid_display_section(self):
        """Setup grid display settings section"""
        controls_layout = self.add_section(
            self.tr("Grid Display Settings"),
            self.tr("Configure grid layout and visual options")
        )
        
        # Grid Layout selection
        grid_layout_row = QHBoxLayout()
        grid_layout_row.addWidget(self.create_label(self.tr("Grid Layout:")))
        
        self.grid_layout_combo = QComboBox()
        grid_layouts = ["1x1", "2x2", "3x3", "4x4", "5x5", "6x6", "7x7", "8x8", "9x9", "10x10", "11x11", "12x12"]
        self.grid_layout_combo.addItems(grid_layouts)
        self.grid_layout_combo.setCurrentText(self.tr("3x3"))
        self.grid_layout_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("grid_layout", value)
        )
        
        grid_layout_row.addWidget(self.grid_layout_combo)
        grid_layout_row.addStretch()
        controls_layout.addLayout(grid_layout_row)
        
        # Show Grid Lines
        self.show_grid_lines_checkbox = QCheckBox(self.tr("Show Grid Lines"))
        self.show_grid_lines_checkbox.setObjectName("show_grid_lines_checkbox")
        self.show_grid_lines_checkbox.setChecked(True)
        self.show_grid_lines_checkbox.toggled.connect(
            lambda checked: self.emit_setting_changed("show_grid_lines", checked)
        )
        controls_layout.addWidget(self.show_grid_lines_checkbox)
        
        # Grid Lines Opacity
        opacity_layout = QHBoxLayout()
        self.opacity_title_label = self.create_label(self.tr("Grid Lines Opacity:"))
        self.opacity_title_label.setObjectName("opacity_title_label")
        opacity_layout.addWidget(self.opacity_title_label)
        
        self.grid_opacity_slider = QSlider(Qt.Horizontal)
        self.grid_opacity_slider.setMinimum(10)  # 0.1
        self.grid_opacity_slider.setMaximum(50)  # 0.5
        self.grid_opacity_slider.setValue(30)    # 0.3
        self.grid_opacity_slider.setFixedWidth(150)
        
        self.opacity_label = self.create_label(self.tr("0.3"))
        self.opacity_label.setObjectName("opacity_label")
        self.opacity_label.setFixedWidth(30)
        
        self.grid_opacity_slider.valueChanged.connect(self.on_opacity_changed)
        
        opacity_layout.addWidget(self.grid_opacity_slider)
        opacity_layout.addWidget(self.opacity_label)
        opacity_layout.addStretch()
        controls_layout.addLayout(opacity_layout)
        
        # Show Camera Labels
        self.show_camera_labels_checkbox = QCheckBox(self.tr("Show Camera Labels"))
        self.show_camera_labels_checkbox.setObjectName("show_camera_labels_checkbox")
        self.show_camera_labels_checkbox.setChecked(True)
        self.show_camera_labels_checkbox.toggled.connect(
            lambda checked: self.emit_setting_changed("show_camera_labels", checked)
        )
        controls_layout.addWidget(self.show_camera_labels_checkbox)

        # Auto Grid Expansion
        self.auto_grid_expansion_checkbox = QCheckBox(self.tr("Auto Grid Expansion"))
        self.auto_grid_expansion_checkbox.setObjectName("auto_grid_expansion_checkbox")
        self.auto_grid_expansion_checkbox.setChecked(True)
        self.auto_grid_expansion_checkbox.toggled.connect(
            lambda checked: self.emit_setting_changed("auto_grid_expansion", checked)
        )
        controls_layout.addWidget(self.auto_grid_expansion_checkbox)

    def setup_video_display_section(self):
        """Setup video display settings section"""
        controls_layout = self.add_section(
            self.tr("Video Display Settings"),
            self.tr("Configure video quality and resolution options")
        )
        
        # Max Decode Resolution
        resolution_layout = QHBoxLayout()
        self.max_resolution_label = self.create_label(self.tr("Max Decode Resolution:"))
        self.max_resolution_label.setObjectName("max_resolution_label")
        resolution_layout.addWidget(self.max_resolution_label)
        
        self.max_resolution_combo = QComboBox()
        resolutions = ["1280x720", "1920x1080", "2560x1440"]
        self.max_resolution_combo.addItems(resolutions)
        self.max_resolution_combo.setCurrentText(self.tr("1280x720"))
        self.max_resolution_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("max_decode_resolution", value)
        )
        
        resolution_layout.addWidget(self.max_resolution_combo)
        resolution_layout.addStretch()
        controls_layout.addLayout(resolution_layout)
        
        # Default Frame Size
        frame_size_layout = QHBoxLayout()
        self.frame_size_label = self.create_label(self.tr("Default Frame Size:"))
        self.frame_size_label.setObjectName("frame_size_label")
        frame_size_layout.addWidget(self.frame_size_label)
        
        self.frame_size_combo = QComboBox()
        frame_sizes = ["1280x720", "1920x1080", "2560x1440"]
        self.frame_size_combo.addItems(frame_sizes)
        self.frame_size_combo.setCurrentText(self.tr("1920x1080"))
        self.frame_size_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("default_frame_size", value)
        )
        
        frame_size_layout.addWidget(self.frame_size_combo)
        frame_size_layout.addStretch()
        controls_layout.addLayout(frame_size_layout)
        
        # Sub-stream Threshold
        threshold_layout = QHBoxLayout()
        self.threshold_label = self.create_label(self.tr("Sub-stream Threshold (cameras):"))
        self.threshold_label.setObjectName("threshold_label")
        threshold_layout.addWidget(self.threshold_label)
        
        self.substream_threshold_spinbox = QSpinBox()
        self.substream_threshold_spinbox.setMinimum(4)
        self.substream_threshold_spinbox.setMaximum(144)
        self.substream_threshold_spinbox.setValue(9)
        self.substream_threshold_spinbox.valueChanged.connect(
            lambda value: self.emit_setting_changed("substream_threshold", value)
        )
        
        threshold_layout.addWidget(self.substream_threshold_spinbox)
        threshold_layout.addStretch()
        controls_layout.addLayout(threshold_layout)
        
        # Aspect Ratio (read-only)
        aspect_layout = QHBoxLayout()
        self.aspect_ratio_title_label = self.create_label(self.tr("Video Aspect Ratio:"))
        self.aspect_ratio_title_label.setObjectName("aspect_ratio_title_label")
        aspect_layout.addWidget(self.aspect_ratio_title_label)
        aspect_label = self.create_label("16:9", "gray")
        aspect_label.setObjectName("aspect_ratio_label")
        aspect_layout.addWidget(aspect_label)
        aspect_layout.addStretch()
        controls_layout.addLayout(aspect_layout)

    def setup_toolbar_section(self):
        """Setup toolbar and UI elements section"""
        controls_layout = self.add_section(
            self.tr("Toolbar & UI Elements"),
            self.tr("Configure which toolbar buttons to display")
        )
        
        # Create checkboxes for toolbar buttons
        toolbar_buttons = [
            ("show_record_button", self.tr("Show Record Button"), Config.ENABLE_RECORD_BUTTON),
            ("show_capture_button", self.tr("Show Capture Button"), Config.ENABLE_CAPTURE_BUTTON),
            ("show_speaker_button", self.tr("Show Speaker Button"), Config.ENABLE_SPEAKER_BUTTON),
            ("show_microphone_button", self.tr("Show Microphone Button"), Config.ENABLE_MICROPHONE_BUTTON),
            ("show_fullscreen_button", self.tr("Show Fullscreen Button"), Config.ENABLE_FULL_SCREEN_BUTTON),
            ("show_grid_button", self.tr("Show Grid Button"), Config.ENABLE_GRID_BUTTON),
            ("show_stream_flow_button", self.tr("Show Stream Flow Button"), Config.ENABLE_STREAM_FLOW_BUTTON),
        ]
        
        self.toolbar_checkboxes = {}
        for setting_name, label_text, default_value in toolbar_buttons:
            checkbox = QCheckBox(self.tr(label_text))
            checkbox.setChecked(default_value)
            checkbox.toggled.connect(
                lambda checked, name=setting_name: self.emit_setting_changed(name, checked)
            )
            self.toolbar_checkboxes[setting_name] = checkbox
            controls_layout.addWidget(checkbox)

    def setup_debug_section(self):
        """Setup debug and information display section"""
        controls_layout = self.add_section(
            self.tr("Debug & Information Display"),
            self.tr("Configure debug information and alerts")
        )
        
        # Create checkboxes for debug options
        debug_options = [
            ("show_fps_debug", self.tr("Show FPS Debug Info"), Config.SHOW_FPS_CAMERA_DEBUG),
            ("show_camera_id_debug", self.tr("Show Camera ID Debug"), Config.SHOW_ID_CAMERA_DEBUG),
            ("show_event_bar", self.tr("Show Event Bar"), Config.ENABLE_EVENT_BAR),
            ("show_warning_alerts", self.tr("Show Warning Alerts"), Config.ENABLE_WARNING_ALERT_CAMERA),
        ]
        
        self.debug_checkboxes = {}
        for setting_name, label_text, default_value in debug_options:
            checkbox = QCheckBox(self.tr(label_text))
            checkbox.setChecked(default_value)
            checkbox.toggled.connect(
                lambda checked, name=setting_name: self.emit_setting_changed(name, checked)
            )
            self.debug_checkboxes[setting_name] = checkbox
            controls_layout.addWidget(checkbox)

    def setup_notification_section(self):
        """Setup notification settings section"""
        controls_layout = self.add_section(
            self.tr("Notification Settings"),
            self.tr("Configure notification display options")
        )
        
        # Notification Duration
        duration_layout = QHBoxLayout()
        self.notification_duration_label = self.create_label(self.tr("Notification Duration:"))
        self.notification_duration_label.setObjectName("notification_duration_label")
        duration_layout.addWidget(self.notification_duration_label)
        
        self.notification_duration_combo = QComboBox()
        durations = ["2s", "3s", "5s", "10s"]
        self.notification_duration_combo.addItems(durations)
        self.notification_duration_combo.setCurrentText(self.tr("3s"))
        self.notification_duration_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("notification_duration", value)
        )
        
        duration_layout.addWidget(self.notification_duration_combo)
        duration_layout.addStretch()
        controls_layout.addLayout(duration_layout)
        
        # Notification Max Width
        width_layout = QHBoxLayout()
        self.notification_width_label = self.create_label(self.tr("Notification Max Width:"))
        self.notification_width_label.setObjectName("notification_width_label")
        width_layout.addWidget(self.notification_width_label)
        
        self.notification_width_combo = QComboBox()
        widths = ["200px", "300px", "400px"]
        self.notification_width_combo.addItems(widths)
        self.notification_width_combo.setCurrentText(self.tr("300px"))
        self.notification_width_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("notification_max_width", value)
        )
        
        width_layout.addWidget(self.notification_width_combo)
        width_layout.addStretch()
        controls_layout.addLayout(width_layout)

    @Slot(int)
    def on_opacity_changed(self, value):
        """Handle grid opacity slider change"""
        opacity = value / 100.0  # Convert to 0.0-0.5 range
        self.opacity_label.setText(f"{opacity:.1f}")
        self.emit_setting_changed("grid_lines_opacity", opacity)

    def load_settings(self):
        """Load settings from storage"""
        # TODO: Implement loading from QSettings
        pass

    def save_settings(self):
        """Save settings to storage"""
        # TODO: Implement saving to QSettings
        pass

    def set_dynamic_stylesheet(self):
        """Apply dynamic theme-based styling"""
        # Call parent styling
        super().set_dynamic_stylesheet()

    def translate_ui(self):
        """Update UI text translations using findChild with ObjectName approach"""
        from PySide6.QtCore import QCoreApplication
        from PySide6.QtWidgets import QLabel, QCheckBox


        # Translation mapping for direct checkboxes and section labels
        translations = {
            # Direct checkboxes
            "show_grid_lines_checkbox": u"Show Grid Lines",
            "show_camera_labels_checkbox": u"Show Camera Labels",
            "auto_grid_expansion_checkbox": u"Auto Grid Expansion",

            # Field labels
            "opacity_title_label": u"Grid Lines Opacity:",
            "max_resolution_label": u"Max Decode Resolution:",
            "frame_size_label": u"Default Frame Size:",
            "threshold_label": u"Sub-stream Threshold (cameras):",
            "aspect_ratio_title_label": u"Video Aspect Ratio:",
            "notification_duration_label": u"Notification Duration:",
            "notification_width_label": u"Notification Max Width:",

            # Special labels
            "aspect_ratio_label": u"16:9",

            # Section titles and subtitles (generated by BaseSettingTab)
            "section_title_grid_display_settings": u"Grid Display Settings",
            "section_subtitle_grid_display_settings": u"Configure grid layout and visual options",
            "section_title_video_display_settings": u"Video Display Settings",
            "section_subtitle_video_display_settings": u"Configure video quality and resolution options",
            "section_title_toolbar__ui_elements": u"Toolbar & UI Elements",
            "section_subtitle_toolbar__ui_elements": u"Configure which toolbar buttons to display",
            "section_title_debug__information_display": u"Debug & Information Display",
            "section_subtitle_debug__information_display": u"Configure debug information and alerts",
            "section_title_notification_settings": u"Notification Settings",
            "section_subtitle_notification_settings": u"Configure notification display options",
        }

        # Translate all widgets using findChild with ObjectName
        for object_name, text_key in translations.items():
            # Try different widget types
            widget = self.findChild(QCheckBox, object_name) or self.findChild(QLabel, object_name)
            if widget:
                translated_text = QCoreApplication.translate("UserInterfaceSettingTab", text_key, None)
                widget.setText(translated_text)
                pass
            else:
                pass

        # Toolbar checkboxes (stored in dictionary)
        toolbar_translations = {
            "show_record_button": u"Show Record Button",
            "show_capture_button": u"Show Capture Button",
            "show_speaker_button": u"Show Speaker Button",
            "show_microphone_button": u"Show Microphone Button",
            "show_fullscreen_button": u"Show Fullscreen Button",
            "show_grid_button": u"Show Grid Button",
            "show_stream_flow_button": u"Show Stream Flow Button",
        }

        for setting_name, checkbox in self.toolbar_checkboxes.items():
            if setting_name in toolbar_translations:
                translated_text = QCoreApplication.translate("UserInterfaceSettingTab", toolbar_translations[setting_name], None)
                checkbox.setText(translated_text)
                pass

        # Debug checkboxes (stored in dictionary)
        debug_translations = {
            "show_fps_debug": u"Show FPS Debug Info",
            "show_camera_id_debug": u"Show Camera ID Debug",
            "show_event_bar": u"Show Event Bar",
            "show_warning_alerts": u"Show Warning Alerts",
        }

        for setting_name, checkbox in self.debug_checkboxes.items():
            if setting_name in debug_translations:
                translated_text = QCoreApplication.translate("UserInterfaceSettingTab", debug_translations[setting_name], None)
                checkbox.setText(translated_text)
                pass

        # Update opacity label (dynamic value)
        current_opacity = self.grid_opacity_slider.value() / 100.0
        self.opacity_label.setText(f"{current_opacity:.1f}")




