from PySide6.QtCore import Slot
from PySide6.QtWidgets import (QCheckBox, QComboBox, QSpinBox, QLineEdit, QPushButton,
                               QListWidget, QHBoxLayout, QVBoxLayout,
                               QFileDialog, QRadioButton, QButtonGroup)
import logging
from src.presentation.setting_screen.widget.base_setting_tab import BaseSettingTab

logger = logging.getLogger(__name__)


class AdvancedSettingTab(BaseSettingTab):
    """Advanced settings tab for performance and system configuration"""

    def __init__(self, parent=None):
        """Initialize the advanced settings tab"""
        super().__init__(parent)
        self.setup_sections()

    def setup_sections(self):
        """Setup all advanced sections"""
        # 1. Performance Settings
        self.setup_performance_section()
        self.add_separator()
        
        # 2. Storage Management
        self.setup_storage_section()
        self.add_separator()
        
        # 3. Network Settings
        self.setup_network_section()
        self.add_separator()
        
        # 4. Debug & Maintenance
        self.setup_debug_maintenance_section()
        self.add_separator()
        
        # 5. Integration Settings
        self.setup_integration_section()

        # Add spacer at the bottom
        self.add_bottom_spacer()

    def setup_performance_section(self):
        """Setup performance settings section"""
        controls_layout = self.add_section(
            self.tr("Performance Settings"),
            self.tr("Configure system resource usage limits")
        )
        
        # CPU Usage Limit
        cpu_layout = QHBoxLayout()
        self.cpu_usage_label = self.create_label(self.tr("CPU Usage Limit:"))
        self.cpu_usage_label.setObjectName("cpu_usage_label")
        cpu_layout.addWidget(self.cpu_usage_label)

        self.cpu_limit_combo = QComboBox()
        cpu_limits = ["25%", "50%", "75%", self.tr("Unlimited")]
        self.cpu_limit_combo.addItems(cpu_limits)
        self.cpu_limit_combo.setCurrentText(self.tr("75%"))
        self.cpu_limit_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("cpu_usage_limit", value)
        )

        cpu_layout.addWidget(self.cpu_limit_combo)
        cpu_layout.addStretch()
        controls_layout.addLayout(cpu_layout)

        # Memory Usage Limit
        memory_layout = QHBoxLayout()
        memory_layout.addWidget(self.create_label(self.tr("Memory Usage Limit:")))

        self.memory_limit_combo = QComboBox()
        memory_limits = ["1GB", "2GB", "4GB", "8GB", self.tr("Unlimited")]
        self.memory_limit_combo.addItems(memory_limits)
        self.memory_limit_combo.setCurrentText(self.tr("4GB"))
        self.memory_limit_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("memory_usage_limit", value)
        )

        memory_layout.addWidget(self.memory_limit_combo)
        memory_layout.addStretch()
        controls_layout.addLayout(memory_layout)

        # Network Bandwidth
        bandwidth_layout = QHBoxLayout()
        bandwidth_layout.addWidget(self.create_label(self.tr("Network Bandwidth:")))

        self.bandwidth_combo = QComboBox()
        bandwidths = ["1Mbps", "10Mbps", "100Mbps", self.tr("Unlimited")]
        self.bandwidth_combo.addItems(bandwidths)
        self.bandwidth_combo.setCurrentText(self.tr("100Mbps"))
        self.bandwidth_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("network_bandwidth", value)
        )

        bandwidth_layout.addWidget(self.bandwidth_combo)
        bandwidth_layout.addStretch()
        controls_layout.addLayout(bandwidth_layout)

        # Hardware Acceleration
        hw_accel_layout = QHBoxLayout()
        hw_accel_layout.addWidget(self.create_label(self.tr("Hardware Acceleration:")))

        self.hw_accel_combo = QComboBox()
        hw_accels = [self.tr("Auto"), "NVIDIA", "Intel", self.tr("Disabled")]
        self.hw_accel_combo.addItems(hw_accels)
        self.hw_accel_combo.setCurrentText(self.tr("Auto"))
        self.hw_accel_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("hardware_acceleration", value)
        )

        hw_accel_layout.addWidget(self.hw_accel_combo)
        hw_accel_layout.addStretch()
        controls_layout.addLayout(hw_accel_layout)

    def setup_storage_section(self):
        """Setup storage management section"""
        controls_layout = self.add_section(
            self.tr("Storage Management"),
            self.tr("Configure recording storage and cleanup options")
        )
        
        # Recording Path
        path_layout = QHBoxLayout()
        self.recording_path_label = self.create_label(self.tr("Recording Path:"))
        self.recording_path_label.setObjectName("recording_path_label")
        path_layout.addWidget(self.recording_path_label)

        self.recording_path_edit = QLineEdit()
        self.recording_path_edit.setObjectName("recording_path_edit")
        self.recording_path_edit.setPlaceholderText(self.tr("Select recording directory..."))
        self.recording_path_edit.textChanged.connect(
            lambda text: self.emit_setting_changed("recording_path", text)
        )

        self.browse_path_button = QPushButton(self.tr("Browse"))
        self.browse_path_button.setObjectName("browse_path_button")
        self.browse_path_button.clicked.connect(self.browse_recording_path)

        path_layout.addWidget(self.recording_path_edit)
        path_layout.addWidget(self.browse_path_button)
        controls_layout.addLayout(path_layout)

        # Max Storage Size
        storage_layout = QHBoxLayout()
        storage_layout.addWidget(self.create_label(self.tr("Max Storage Size:")))

        self.max_storage_combo = QComboBox()
        storage_sizes = ["100GB", "500GB", "1TB", "2TB", self.tr("Unlimited")]
        self.max_storage_combo.addItems(storage_sizes)
        self.max_storage_combo.setCurrentText(self.tr("1TB"))
        self.max_storage_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("max_storage_size", value)
        )

        storage_layout.addWidget(self.max_storage_combo)
        storage_layout.addStretch()
        controls_layout.addLayout(storage_layout)

        # Auto-cleanup
        cleanup_layout = QHBoxLayout()
        cleanup_layout.addWidget(self.create_label(self.tr("Auto-cleanup:")))

        self.auto_cleanup_combo = QComboBox()
        cleanup_periods = [self.tr("7 days"), self.tr("30 days"), self.tr("90 days"), self.tr("Never")]
        self.auto_cleanup_combo.addItems(cleanup_periods)
        self.auto_cleanup_combo.setCurrentText(self.tr("30 days"))
        self.auto_cleanup_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("auto_cleanup", value)
        )

        cleanup_layout.addWidget(self.auto_cleanup_combo)
        cleanup_layout.addStretch()
        controls_layout.addLayout(cleanup_layout)

        # Compression
        compression_layout = QHBoxLayout()
        compression_layout.addWidget(self.create_label(self.tr("Video Compression:")))

        self.compression_combo = QComboBox()
        compressions = ["H.264", "H.265", "MJPEG"]
        self.compression_combo.addItems(compressions)
        self.compression_combo.setCurrentText(self.tr("H.264"))
        self.compression_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("video_compression", value)
        )

        compression_layout.addWidget(self.compression_combo)
        compression_layout.addStretch()
        controls_layout.addLayout(compression_layout)

    def setup_network_section(self):
        """Setup network settings section"""
        controls_layout = self.add_section(
            self.tr("Network Settings"),
            self.tr("Configure network ports and protocols")
        )
        
        # RTSP Port
        rtsp_layout = QHBoxLayout()
        rtsp_layout.addWidget(self.create_label(self.tr("RTSP Port:")))

        self.rtsp_port_spinbox = QSpinBox()
        self.rtsp_port_spinbox.setMinimum(1)
        self.rtsp_port_spinbox.setMaximum(65535)
        self.rtsp_port_spinbox.setValue(554)
        self.rtsp_port_spinbox.valueChanged.connect(
            lambda value: self.emit_setting_changed("rtsp_port", value)
        )

        rtsp_layout.addWidget(self.rtsp_port_spinbox)
        rtsp_layout.addStretch()
        controls_layout.addLayout(rtsp_layout)

        # HTTP Port
        http_layout = QHBoxLayout()
        http_layout.addWidget(self.create_label(self.tr("HTTP Port:")))

        self.http_port_spinbox = QSpinBox()
        self.http_port_spinbox.setMinimum(1)
        self.http_port_spinbox.setMaximum(65535)
        self.http_port_spinbox.setValue(80)
        self.http_port_spinbox.valueChanged.connect(
            lambda value: self.emit_setting_changed("http_port", value)
        )

        http_layout.addWidget(self.http_port_spinbox)
        http_layout.addStretch()
        controls_layout.addLayout(http_layout)

        # HTTPS Port
        https_layout = QHBoxLayout()
        https_layout.addWidget(self.create_label(self.tr("HTTPS Port:")))

        self.https_port_spinbox = QSpinBox()
        self.https_port_spinbox.setMinimum(1)
        self.https_port_spinbox.setMaximum(65535)
        self.https_port_spinbox.setValue(443)
        self.https_port_spinbox.valueChanged.connect(
            lambda value: self.emit_setting_changed("https_port", value)
        )

        https_layout.addWidget(self.https_port_spinbox)
        https_layout.addStretch()
        controls_layout.addLayout(https_layout)
        
        # Multicast
        self.multicast_checkbox = QCheckBox(self.tr("Enable Multicast"))
        self.multicast_checkbox.setObjectName("multicast_checkbox")
        self.multicast_checkbox.setChecked(False)
        self.multicast_checkbox.toggled.connect(
            lambda checked: self.emit_setting_changed("enable_multicast", checked)
        )
        controls_layout.addWidget(self.multicast_checkbox)
        
        # Buffer Size
        buffer_layout = QHBoxLayout()
        buffer_layout.addWidget(self.create_label(self.tr("Buffer Size:")))

        self.buffer_size_combo = QComboBox()
        buffer_sizes = ["1MB", "5MB", "10MB", "50MB"]
        self.buffer_size_combo.addItems(buffer_sizes)
        self.buffer_size_combo.setCurrentText(self.tr("10MB"))
        self.buffer_size_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("buffer_size", value)
        )

        buffer_layout.addWidget(self.buffer_size_combo)
        buffer_layout.addStretch()
        controls_layout.addLayout(buffer_layout)

    def setup_debug_maintenance_section(self):
        """Setup debug and maintenance section"""
        controls_layout = self.add_section(
            self.tr("Debug & Maintenance"),
            self.tr("Configure logging and system maintenance options")
        )
        
        # Log Level
        log_level_layout = QHBoxLayout()
        log_level_layout.addWidget(self.create_label(self.tr("Log Level:")))

        self.debug_log_level_combo = QComboBox()
        log_levels = [self.tr("Debug"), self.tr("Info"), self.tr("Warning"), self.tr("Error")]
        self.debug_log_level_combo.addItems(log_levels)
        self.debug_log_level_combo.setCurrentText(self.tr("Info"))
        self.debug_log_level_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("debug_log_level", value)
        )

        log_level_layout.addWidget(self.debug_log_level_combo)
        log_level_layout.addStretch()
        controls_layout.addLayout(log_level_layout)

        # Log File Size
        log_size_layout = QHBoxLayout()
        log_size_layout.addWidget(self.create_label(self.tr("Log File Size:")))

        self.log_file_size_combo = QComboBox()
        log_sizes = ["10MB", "50MB", "100MB", "500MB"]
        self.log_file_size_combo.addItems(log_sizes)
        self.log_file_size_combo.setCurrentText(self.tr("50MB"))
        self.log_file_size_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("log_file_size", value)
        )

        log_size_layout.addWidget(self.log_file_size_combo)
        log_size_layout.addStretch()
        controls_layout.addLayout(log_size_layout)
        
        # Auto-restart
        self.auto_restart_checkbox = QCheckBox(self.tr("Enable Auto-restart on Crash"))
        self.auto_restart_checkbox.setObjectName("auto_restart_checkbox")
        self.auto_restart_checkbox.setChecked(True)
        self.auto_restart_checkbox.toggled.connect(
            lambda checked: self.emit_setting_changed("auto_restart", checked)
        )
        controls_layout.addWidget(self.auto_restart_checkbox)
        
        # Crash Reports
        crash_layout = QVBoxLayout()
        crash_layout.addWidget(self.create_label(self.tr("Crash Reports:")))

        self.crash_reports_group = QButtonGroup()
        self.send_auto_radio = QRadioButton(self.tr("Send automatically"))
        self.send_auto_radio.setObjectName("send_auto_radio")
        self.ask_radio = QRadioButton(self.tr("Ask before sending"))
        self.ask_radio.setObjectName("ask_radio")
        self.never_send_radio = QRadioButton(self.tr("Never send"))
        self.never_send_radio.setObjectName("never_send_radio")
        self.ask_radio.setChecked(True)

        self.crash_reports_group.addButton(self.send_auto_radio, 0)
        self.crash_reports_group.addButton(self.ask_radio, 1)
        self.crash_reports_group.addButton(self.never_send_radio, 2)
        self.crash_reports_group.buttonClicked.connect(self.on_crash_reports_changed)

        crash_layout.addWidget(self.send_auto_radio)
        crash_layout.addWidget(self.ask_radio)
        crash_layout.addWidget(self.never_send_radio)
        controls_layout.addLayout(crash_layout)

        # Database Maintenance
        db_layout = QHBoxLayout()
        db_layout.addWidget(self.create_label(self.tr("Database Maintenance:")))

        self.db_maintenance_combo = QComboBox()
        db_options = [self.tr("Auto-optimize"), self.tr("Manual"), self.tr("Disabled")]
        self.db_maintenance_combo.addItems(db_options)
        self.db_maintenance_combo.setCurrentText(self.tr("Auto-optimize"))
        self.db_maintenance_combo.currentTextChanged.connect(
            lambda value: self.emit_setting_changed("database_maintenance", value)
        )

        db_layout.addWidget(self.db_maintenance_combo)
        db_layout.addStretch()
        controls_layout.addLayout(db_layout)

    def setup_integration_section(self):
        """Setup integration settings section"""
        controls_layout = self.add_section(
            self.tr("Integration Settings"),
            self.tr("Configure API access and third-party integrations")
        )
        
        # API Access
        self.api_access_checkbox = QCheckBox(self.tr("Enable API Access"))
        self.api_access_checkbox.setObjectName("api_access_checkbox")
        self.api_access_checkbox.setChecked(False)
        self.api_access_checkbox.toggled.connect(
            lambda checked: self.emit_setting_changed("api_access", checked)
        )
        controls_layout.addWidget(self.api_access_checkbox)
        
        # Webhook URLs
        webhook_layout = QVBoxLayout()
        webhook_layout.addWidget(self.create_label(self.tr("Webhook URLs:")))

        self.webhook_list = QListWidget()
        self.webhook_list.setMaximumHeight(100)

        webhook_buttons_layout = QHBoxLayout()
        self.add_webhook_button = QPushButton(self.tr("Add"))
        self.add_webhook_button.setObjectName("add_webhook_button")
        self.edit_webhook_button = QPushButton(self.tr("Edit"))
        self.edit_webhook_button.setObjectName("edit_webhook_button")
        self.delete_webhook_button = QPushButton(self.tr("Delete"))
        self.delete_webhook_button.setObjectName("delete_webhook_button")

        self.add_webhook_button.clicked.connect(self.add_webhook)
        self.edit_webhook_button.clicked.connect(self.edit_webhook)
        self.delete_webhook_button.clicked.connect(self.delete_webhook)

        webhook_buttons_layout.addWidget(self.add_webhook_button)
        webhook_buttons_layout.addWidget(self.edit_webhook_button)
        webhook_buttons_layout.addWidget(self.delete_webhook_button)
        webhook_buttons_layout.addStretch()

        webhook_layout.addWidget(self.webhook_list)
        webhook_layout.addLayout(webhook_buttons_layout)
        controls_layout.addLayout(webhook_layout)

        # Third-party Plugins
        self.plugins_checkbox = QCheckBox(self.tr("Enable Third-party Plugins"))
        self.plugins_checkbox.setObjectName("plugins_checkbox")
        self.plugins_checkbox.setChecked(False)
        self.plugins_checkbox.toggled.connect(
            lambda checked: self.emit_setting_changed("third_party_plugins", checked)
        )
        controls_layout.addWidget(self.plugins_checkbox)

        # Export Formats
        export_layout = QVBoxLayout()
        export_layout.addWidget(self.create_label(self.tr("Export Formats:")))
        
        self.avi_checkbox = QCheckBox(self.tr("AVI"))
        self.avi_checkbox.setObjectName("avi_checkbox")
        self.mp4_checkbox = QCheckBox(self.tr("MP4"))
        self.mp4_checkbox.setObjectName("mp4_checkbox")
        self.mov_checkbox = QCheckBox(self.tr("MOV"))
        self.mov_checkbox.setObjectName("mov_checkbox")
        
        self.avi_checkbox.setChecked(True)
        self.mp4_checkbox.setChecked(True)
        self.mov_checkbox.setChecked(False)
        
        self.avi_checkbox.toggled.connect(lambda checked: self.emit_setting_changed("export_avi", checked))
        self.mp4_checkbox.toggled.connect(lambda checked: self.emit_setting_changed("export_mp4", checked))
        self.mov_checkbox.toggled.connect(lambda checked: self.emit_setting_changed("export_mov", checked))
        
        export_layout.addWidget(self.avi_checkbox)
        export_layout.addWidget(self.mp4_checkbox)
        export_layout.addWidget(self.mov_checkbox)
        controls_layout.addLayout(export_layout)

    # Event handlers
    @Slot()
    def browse_recording_path(self):
        """Handle browse recording path button click"""
        directory = QFileDialog.getExistingDirectory(
            self, 
            self.tr("Select Recording Directory"),
            self.recording_path_edit.text()
        )
        if directory:
            self.recording_path_edit.setText(directory)

    @Slot()
    def on_crash_reports_changed(self):
        """Handle crash reports radio button change"""
        button_id = self.crash_reports_group.checkedId()
        options = [self.tr("send_auto"), self.tr("ask"), self.tr("never")]
        self.emit_setting_changed("crash_reports", options[button_id])

    @Slot()
    def add_webhook(self):
        """Handle add webhook button click"""
        self.emit_setting_changed("webhook_action", "add")

    @Slot()
    def edit_webhook(self):
        """Handle edit webhook button click"""
        self.emit_setting_changed("webhook_action", "edit")

    @Slot()
    def delete_webhook(self):
        """Handle delete webhook button click"""
        self.emit_setting_changed("webhook_action", "delete")

    def load_settings(self):
        """Load settings from storage"""
        pass

    def save_settings(self):
        """Save settings to storage"""
        pass

    def set_dynamic_stylesheet(self):
        """Apply dynamic theme-based styling"""
        # Call parent styling
        super().set_dynamic_stylesheet()

    def translate_ui(self):
        """Update UI text translations using findChild with ObjectName approach"""
        from PySide6.QtCore import QCoreApplication
        from PySide6.QtWidgets import QCheckBox, QPushButton, QRadioButton, QLabel


        # Translation mapping
        translations = {
            # Buttons
            "browse_path_button": u"Browse",
            "add_webhook_button": u"Add",
            "edit_webhook_button": u"Edit",
            "delete_webhook_button": u"Delete",

            # Checkboxes
            "multicast_checkbox": u"Enable Multicast",
            "auto_restart_checkbox": u"Enable Auto-restart on Crash",
            "api_access_checkbox": u"Enable API Access",
            "plugins_checkbox": u"Enable Third-party Plugins",
            "avi_checkbox": u"AVI",
            "mp4_checkbox": u"MP4",
            "mov_checkbox": u"MOV",

            # Radio buttons - Crash Reports
            "send_auto_radio": u"Send automatically",
            "ask_radio": u"Ask before sending",
            "never_send_radio": u"Never send",

            # Field labels
            "cpu_usage_label": u"CPU Usage Limit:",
            "recording_path_label": u"Recording Path:",

            # Section titles and subtitles (generated by BaseSettingTab)
            "section_title_performance_settings": u"Performance Settings",
            "section_subtitle_performance_settings": u"Configure system resource usage limits",
            "section_title_storage_management": u"Storage Management",
            "section_subtitle_storage_management": u"Configure recording storage and cleanup options",
            "section_title_network_settings": u"Network Settings",
            "section_subtitle_network_settings": u"Configure network ports and protocols",
            "section_title_debug__maintenance": u"Debug & Maintenance",
            "section_subtitle_debug__maintenance": u"Configure logging and system maintenance options",
            "section_title_integration_settings": u"Integration Settings",
            "section_subtitle_integration_settings": u"Configure API access and third-party integrations",
        }

        # Translate all widgets using findChild with ObjectName
        for object_name, text_key in translations.items():
            # Try different widget types
            widget = (self.findChild(QCheckBox, object_name) or
                     self.findChild(QPushButton, object_name) or
                     self.findChild(QRadioButton, object_name) or
                     self.findChild(QLabel, object_name))

            # Check if widget exists as instance variable
            if not widget and hasattr(self, object_name):
                widget = getattr(self, object_name)

            if widget and hasattr(widget, 'setText'):
                translated_text = QCoreApplication.translate("AdvancedSettingTab", text_key, None)
                widget.setText(translated_text)
            else:
                pass

        # Translate combo box items manually
        self._translate_combo_boxes()

        # Translate placeholder text
        self._translate_placeholder_texts()



    def _translate_placeholder_texts(self):
        """Translate placeholder texts for input widgets"""
        from PySide6.QtCore import QCoreApplication

        # Recording Path placeholder
        placeholder_text = QCoreApplication.translate("AdvancedSettingTab", u"Select recording directory...", None)
        self.recording_path_edit.setPlaceholderText(placeholder_text)

    def _translate_combo_boxes(self):
        """Translate combo box items that need manual translation"""
        from PySide6.QtCore import QCoreApplication

        # CPU Limit combo
        current_cpu = self.cpu_limit_combo.currentIndex()
        self.cpu_limit_combo.clear()
        self.cpu_limit_combo.addItems([
            "25%", "50%", "75%",
            QCoreApplication.translate("AdvancedSettingTab", u"Unlimited", None)
        ])
        if current_cpu < self.cpu_limit_combo.count():
            self.cpu_limit_combo.setCurrentIndex(current_cpu)

        # Memory Limit combo
        current_memory = self.memory_limit_combo.currentIndex()
        self.memory_limit_combo.clear()
        self.memory_limit_combo.addItems([
            "1GB", "2GB", "4GB", "8GB",
            QCoreApplication.translate("AdvancedSettingTab", u"Unlimited", None)
        ])
        if current_memory < self.memory_limit_combo.count():
            self.memory_limit_combo.setCurrentIndex(current_memory)

        # Network Bandwidth combo
        current_bandwidth = self.bandwidth_combo.currentIndex()
        self.bandwidth_combo.clear()
        self.bandwidth_combo.addItems([
            "1Mbps", "10Mbps", "100Mbps",
            QCoreApplication.translate("AdvancedSettingTab", u"Unlimited", None)
        ])
        if current_bandwidth < self.bandwidth_combo.count():
            self.bandwidth_combo.setCurrentIndex(current_bandwidth)

        # Hardware Acceleration combo
        current_hw = self.hw_accel_combo.currentIndex()
        self.hw_accel_combo.clear()
        self.hw_accel_combo.addItems([
            QCoreApplication.translate("AdvancedSettingTab", u"Auto", None),
            "NVIDIA", "Intel",
            QCoreApplication.translate("AdvancedSettingTab", u"Disabled", None)
        ])
        if current_hw < self.hw_accel_combo.count():
            self.hw_accel_combo.setCurrentIndex(current_hw)

        # Max Storage combo
        current_storage = self.max_storage_combo.currentIndex()
        self.max_storage_combo.clear()
        self.max_storage_combo.addItems([
            "100GB", "500GB", "1TB", "2TB",
            QCoreApplication.translate("AdvancedSettingTab", u"Unlimited", None)
        ])
        if current_storage < self.max_storage_combo.count():
            self.max_storage_combo.setCurrentIndex(current_storage)

        # Auto-cleanup combo
        current_cleanup = self.auto_cleanup_combo.currentIndex()
        self.auto_cleanup_combo.clear()
        self.auto_cleanup_combo.addItems([
            QCoreApplication.translate("AdvancedSettingTab", u"7 days", None),
            QCoreApplication.translate("AdvancedSettingTab", u"30 days", None),
            QCoreApplication.translate("AdvancedSettingTab", u"90 days", None),
            QCoreApplication.translate("AdvancedSettingTab", u"Never", None)
        ])
        if current_cleanup < self.auto_cleanup_combo.count():
            self.auto_cleanup_combo.setCurrentIndex(current_cleanup)

        # Debug Log Level combo
        current_debug_log = self.debug_log_level_combo.currentIndex()
        self.debug_log_level_combo.clear()
        self.debug_log_level_combo.addItems([
            QCoreApplication.translate("AdvancedSettingTab", u"Debug", None),
            QCoreApplication.translate("AdvancedSettingTab", u"Info", None),
            QCoreApplication.translate("AdvancedSettingTab", u"Warning", None),
            QCoreApplication.translate("AdvancedSettingTab", u"Error", None)
        ])
        if current_debug_log < self.debug_log_level_combo.count():
            self.debug_log_level_combo.setCurrentIndex(current_debug_log)

        # Database Maintenance combo
        current_db = self.db_maintenance_combo.currentIndex()
        self.db_maintenance_combo.clear()
        self.db_maintenance_combo.addItems([
            QCoreApplication.translate("AdvancedSettingTab", u"Auto-optimize", None),
            QCoreApplication.translate("AdvancedSettingTab", u"Manual", None),
            QCoreApplication.translate("AdvancedSettingTab", u"Disabled", None)
        ])
        if current_db < self.db_maintenance_combo.count():
            self.db_maintenance_combo.setCurrentIndex(current_db)
