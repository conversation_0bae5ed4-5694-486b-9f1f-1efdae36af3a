class LightThemeColor:
    class Color:
        # COMMON
        text = "#2B2A3A"
        text_same_bg = "#ffffff"
        text_pressed = "#51d460"
        text_yellow = "#D4A000" 
        primary = '#51A960'
        test = "#00ffff"
        button_background_normal = "#51A960"
        button_background_chosen = "#51A960"
        border_color = "#CDCDCD"
        hover_color = "#F0F0F0"
        login_dialog_background = "rgba(250, 250, 250, 0.9)"  # Nền mờ sáng 50%
        background_overlay = "rgba(0,0,0,0.1)"

        # Video Control Colors
        video_control_button_normal = "#ECECEC"
        video_control_button_active = "#51A960"
        video_control_button_hover = "#D1D1D1"
        video_control_text_normal = "#404040"
        video_control_text_active = "#FFFFFF"
        video_control_text_hover = "#51A960"  # Primary color for hover effect
        video_control_background = "#F5F5F5"
        video_control_border = "#CDCDCD"
        video_control_speed_normal = "#ECECEC"
        video_control_speed_active = "#51A960"
        video_control_speed_hover = "#D1D1D1"
        video_control_speed_active_hover = "#51A960"

        # Status Colors
        error_color = "#B5122E"

        label_title_1 = "#404040"

        widget_background_1 = "#F5F5F5"
        widget_background_2 = "#D7D7D7"

        dialog_header_background = "#51A960"
        dialog_body_background = "#FFFFFF"
        dialog_button_background = "#EEEEEE"
        dialog_text = "#2B2A3A"
        dialog_border = "#CDCDCD"  # Using common border color

        input_border = "#CDCDCD"
        input_background = "#F5F5F5"
        input_text = "#2B2A3A"
        
        tabbar_background_normal = "#F5F5F5"
        tabbar_background_selected = "rgba(81, 169, 96, 0.2)"
        tabbar_text_normal = "#51A960"
        tabbar_text_selected = "#F5F5F5"

        subtabbar_text_normal = "#51A960"

        treeview_item_background_selected = "#ECEBF3"
        treeview_on_hover = "#DDDDDD"

        sidetab_border = "#CDCDCD"
        main_border = "#D0D0D0"
        main_background = "#F5F5F5"
        main_background_splitter = "#F5F5F5"
        bubble_background = "#C4DEC8"

        # SERVER ITEM
        server_item_background_off = "#F5F5F5"
        server_item_background_active = "#d2f6e0"
        server_item_background_inactive = "transparent"
        server_item_background_hoverred = "transparent"
        server_item_title = "#1C1C1C"
        server_item_border_hoverred = "#51A960"
        server_item_edit_button_background_hoverred = "#e0e0e0"

        # FILTER COLOR
        filter_background = "#E4E5E7"

        # ADD SERVER WIDGET
        add_server_widget_background = "#F5F5F5"
        add_server_sub_title = "black"
        add_server_main_title = "black"
        add_server_lineedit_focused = "black"

        # SEARCH BAR
        search_server_background = "#F5F5F5"
        search_server_lineedit_background = "#F5F5F5"
        search_server_lineedit_border = "#51d460"
        search_server_lineedit_placeholder = "#525252"
        search_server_border = "transparent"

        titlebar_close_hoverred = "#B5122E"

        # TABLE WIDGET
        table_row_hoverred = "#D9D9D9"
        table_row_text = "#1C1C1C"
        table_action_background_hoverred = "#bebebe"
        table_odd_row_background = "#F5F5F5"
        table_even_row_background = "#ECEBF3"
        table_header_background = "#D3D3D3"
        table_header_text = "#51A960"
        table_page_indicator_background = "#ECECEC"
        table_page_indicator_text = "#717171"
        table_pagination_background = "#F5F5F5"
        table_pagination_text = "#1C1C1C"
        table_pagination_background_selected = "#51A960"

        table_normal_button_background = "#C2C2C2"
        table_normal_button_background_hoverred = "#CDCDCD"
        table_normal_button_text = "#717171"
        table_pagination_text_selected = "#F5F5F5"

        table_item_header_background = "#EFEFF5"
        table_item_header_name_text = "#51A960"
        table_item_header_text = "#262626"

        table_edit_item_background = "transparent"
        table_edit_item_background_hoverred = "#EFEFF5"

        # CAMERA WIDGET
        camera_widget_background = '#ECECEC'
        camera_widget_background_clicked = '#ADADCF'
        camera_widget_text_clicked = '#51A960'

        # text color
        text_color_all_app = '#000000'
        text_disable = '#C2C2C2'
        text_on_primary = '#F5F5F5'

        # COMBOBOX
        combobox_item_active_background = "#51A960"
        combobox_item_inactive_background = "#FBFBFB"
        dialog_close_hover_bg = "#A5A3C7"

        # HOVER COLOR
        hover_button = '#A5A3C7'

        # DIVIDER
        divider = '#CDCDCD'
        common_border = '#D3D3D3'
        tabbar_border = '#D3D3D3'

        # NODE GRAPHIC
        r_node_graphic_background = 209
        g_node_graphic_background = 209
        b_node_graphic_background = 209

        # DRAW AI ZONE
        ai_zone_background = "#ECECEC"

        # AI BUTTON STATUS COLOR
        ai_checkbox_background_disabled = "transparent"
        ai_checkbox_background_off = "transparent"
        ai_checkbox_background_on = "#E7EBFA"
        ai_checkbox_text_enabled = "#262626"
        ai_checkbox_text_disabled = "#7B7B7B"

        ai_button_background_off = "#E9E9E9"
        ai_button_background_on = "#4570DD"
        ai_button_text_on = "#FFFFFF"
        ai_button_text_off = "#262626"

        file_button_focus = "#51d460"

        # TimeLineController
        timeline_background_disabled = "#F5F5F5"
        timeline_background_off = "#E4E5E7"
        timeline_background_on = "#DCEEDF"
        timeline_border_off = "#C3C5C9"
        timeline_icon = "#000000"
        timeline_text_off = "#262626"
        timeline_text_on = "#FFFFFF"
        daytime_background_on = "#F6BE00"
        #########################
        white = "#FFFFFF"
        black = "#000000"
        default = "efefef"
        # primary = "#5C687F"
        green = '#1CD1A1'
        primary_hover = "#3F7A47"
        primary_pressed = "#3F7A47"
        cell_item = "#ced0d4"
        cell_header = "#a8bdad"
        secondary = "#2B2A3A"
        background = "#D7D7D7"
        on_background = "#51A960"
        on_hover = "#656475"
        on_hover_primary = "#3F7A47"
        on_hover_secondary = "#656475"
        on_hover_button = "#3F7A47"
        hover_button_toolbar = "#656475"
        background_item = "#363546"
        background_item_off = "#2F2E3C"
        # Text color
        white = "#FFFFFF"
        text_black = "#2B2A3A"
        text_selected = "#FFFFFF"
        text_unselected = "#656475"
        white_2 = "#F7F0F7"
        text_title_color = "#404040"
        text_place_holder = "#656475"
        # text_disable = "#656475"
        disable_color = "#656475"
        text_on_primary = '#EEEEEE'
        text_not_select = "#F7F0F7"
        text_note = '#575757'
        # border and stroke
        border_line_edit = "#F7F0F7"
        border_item = "#979797"
        # divider = "#656475"
        border_line_edit_not_focus = "#CDCDCD"
        # Button
        button_second_background = "#51A960"
        button_primary_background = "#51A960"
        button_disable_background = "#F5F5F5"
        # Menu
        menu_title = "#A5A5A5"

        available = "#48DC6B"
        unavailable = "#CC5051"
        status_appear = "rgba(61, 173, 254, 1)"
        status_checkin = "rgba(19, 172, 25, 1)"
        status_checkout = "rgba(204, 80, 81, 1)"
        text_camera_name = "#51A960"
        # divider = "rgba(42, 43, 50, 1)"
        background_search_bar_event = "rgba(2, 2, 3, 0.45)"
        text_search_bar_event = "rgba(255, 255, 255, 0.35)"
        status_appear_dialog = "#41A0FA"
        # event_number_color ='rgba(255, 145, 94, 1)'
        event_number_color = "#FF915E"
        button_color = "#3F7A47"
        button_disable = "#DFE0E4"
        border_button = "#51A960"
        error = "#B5122E"
        pulse_toggle_color = "#FFFFFF"
        background_warning = "#CCCCD1"
        transparent = "rgba(0, 0, 0, 0)"
        button_upload = "#3388DC"
        background_dialog = "#FFFFFF"
        background_box = "#E99899"
        background_box_hover = "#CC5051"
        server_connected = "#1CD1A1"
        widget_disable = "3A3A3A"
        
        # Calendar colors
        calendar_selection_text = "#FFFFFF"
        calendar_selection_bg = "#51A960"  # Using primary color
        calendar_background = "#FFFFFF"    # White background
        calendar_text = "#2B2A3A"         # Dark text
        calendar_border = "#E0E0E0"       # Light border
        calendar_menu_bg = "#FFFFFF"      # White background
        calendar_menu_text = "#2B2A3A"    # Dark text
        
        # Time picker colors
        time_picker_bg = "#FFFFFF"        # White background
        time_picker_text = "#2B2A3A"      # Dark text
        time_picker_border = "#E0E0E0"    # Light border
        time_picker_border_hover = "#51A960"  # Using primary color
    
        # GridItem
        grid_item_hover_border = "#FF4500"
        grid_item_drag_border = "#FF6B6B"
    class Image:
        ##############################
        #language
        flag_united_kingdom = ":src/assets/tool_icons/flag_united_kingdom.svg"
        flag_russia = ":src/assets/tool_icons/flag_russia.svg"

        # CUSTOM MAIN TAB BAR
        server_item_edit_icon = ":src/assets/login_screen/edit_server_light.svg"
        server_tab_off = ":src/assets/side_menu_icon/server_screen_off_light.svg"
        server_tab_on = ":src/assets/side_menu_icon/server_screen_on_light.svg"

        streaming_tab_not_select = ":src/assets/side_menu_icon/streaming_screen_off_light.svg"
        streaming_tab_select = ":src/assets/side_menu_icon/streaming_screen_on_light.svg"

        device_tab_not_select = ":src/assets/side_menu_icon/device_screen_off_light.svg"
        device_tab_select = ":src/assets/side_menu_icon/device_screen_on_light.svg"
        
        user_tab_select = ":src/assets/side_menu_icon/user_screen_on_light.svg"
        user_tab_not_select = ":src/assets/side_menu_icon/user_screen_off_light.svg"

        setting_tab_select = ":src/assets/side_menu_icon/setting_screen_on_light.svg"
        setting_tab_not_select = ":src/assets/side_menu_icon/setting_screen_off_light.svg"
        logo_login = ":src/assets/login_screen/logo_login_light.svg"
        logo_login_mini = ":src/assets/login_screen/logo_login_light_mini.svg"

        # TITLE BAR ICON
        titlebar_close_application_normal = ":src/assets/title_bar/close_window_black.svg"
        titlebar_minimize_window_normal = ":src/assets/title_bar/minimize_window_black.svg"
        titlebar_maximize_window_normal = ":src/assets/title_bar/maximize_window_black.svg"
        titlebar_settings_application_normal = ":src/assets/title_bar/settings_black.svg"
        titlebar_normal_window_normal = ":/src/assets/title_bar/normal_window_black.svg"

        # CHECKBOX
        checkbox_checked_qml = "qrc:src/assets/tool_icons/checkbox_checked_qml_light.svg"
        checkbox_checked = ":src/assets/tool_icons/checkbox_checked_light.svg"
        checkbox_unchecked = ":src/assets/tool_icons/checkbox_unchecked_light.svg"
        checkbox_partially_checked = ":src/assets/tool_icons/checkbox_partially_checked_light.svg"

        table_eye = ":/src/assets/images/eye_in_table_light.svg"
        table_edit = ":/src/assets/images/edit_in_table_light.svg"
        table_trash = ":src/assets/tool_icons/trash_light.svg"
        

        refresh = ":src/assets/event/refresh_light.svg"
        delete = ":/src/assets/images/delete_icon_light.svg"

        dialog_close = ":src/assets/tool_icons/close_dialog_white.svg"
        dialog_close_hoverred = ":src/assets/tool_icons/close_dialog_white.svg"
        copy_schedule_to = "qrc:src/assets/tool_icons/copy_schedule_to_icon_light.svg"
        # TREE VIEW
        treeview_expand_item = ":src/assets/treeview_and_menu_treeview/expand_treeview_light.svg"
        treeview_collapse_item = ":src/assets/treeview_and_menu_treeview/collapse_treeview_light.svg"
        search = ":src/assets/treeview_and_menu_treeview/search_loop_light.svg"
        group_camera_treeview = ":src/assets/treeview_and_menu_treeview/group_camera_treeview_light.svg"
        treeview_server = ":src/assets/treeview_and_menu_treeview/treeview_server_light.svg"
        list_devices = ":src/assets/treeview_and_menu_treeview/list_devices_light.svg"
        list_map = ":src/assets/treeview_and_menu_treeview/list_map_light.svg"
        list_virtual_window = ":src/assets/treeview_and_menu_treeview/list_virtual_windows_light.svg"
        list_save_view = ":src/assets/treeview_and_menu_treeview/list_save_view_light.svg"
        close_all_virtual = ":/src/assets/treeview_and_menu_treeview/close_all_virtual_light.svg"
        open_all_virtual = ":/src/assets/treeview_and_menu_treeview/open_all_virtual_light.svg"
        building_on = ":/src/assets/map/building_on.svg"
        building_off = ":/src/assets/map/building_off_light.svg"
        change_mode = ":src/assets/treeview_and_menu_treeview/change_mode_light.svg"

        # MAP
        icon_background_map = "qrc:src/assets/map/icon_arc_background_light.svg"
        
        # GRID
        grid_1x1 = ":src/assets/grid/grid_1x1_light.svg"
        grid_2x2 = ":src/assets/grid/grid_2x2_light.svg"
        grid_3x3 = ":src/assets/grid/grid_3x3_light.svg"
        grid_4x4 = ":src/assets/grid/grid_4x4_light.svg"
        grid_5x5 = ":src/assets/grid/grid_5x5_light.svg"
        grid_6x6 = ":src/assets/grid/grid_6x6_light.svg"
        grid_8x8 = ":src/assets/grid/grid_8x8_light.svg"
        custom_8_grid = ":src/assets/grid/custom_8_grid_light.svg"
        custom_6_grid = ":src/assets/grid/custom_6_grid_light.svg"
        custom_10_grid = ":src/assets/grid/custom_10_grid_light.svg"
        custom_13_grid = ":src/assets/grid/custom_13_grid_light.svg"
        edit_layout_grid = ":src/assets/grid/edit_layout_grid_light.svg"
        # BOTTOM TOOL BAR
        grid_off = ":src/assets/bottom_toolbar/grid_off_light.svg"
        stream_flow_off = ":src/assets/bottom_toolbar/stream_flow_off_light.svg"
        stream_flow_2_off = ":src/assets/bottom_toolbar/stream_flow_2_off_light.svg"
        stream_flow_3_off = ":src/assets/bottom_toolbar/stream_flow_3_off_light.svg"
        exit_stream_off = ":src/assets/bottom_toolbar/exit_stream_off_light.svg"

        # SPLITTER
        icon_sidebar_big_left = ":src/assets/splitter/splitter_arrow_left_light.svg"
        icon_status_sidebar = ":src/assets/splitter/icon_status_sidebar_light.svg"
        icon_sidebar_big_right = ":src/assets/splitter/splitter_arrow_right_light.svg"
        sidebar_filter = ":src/assets/side_menu_icon/filter_light.svg"

        # EVENT BAR
        lightning_on = ":src/assets/event/lightning_on_light.svg"
        lightning_off = ":src/assets/event/lightning_off_light.svg"
        alert_off = ":src/assets/event/alert_off_light.svg"
        alert_on = ":src/assets/event/alert_on_light.svg"
        calendar = ":src/assets/event/calendar_light.svg"
        ic_filter = ":src/assets/event/filter_light.svg"

        event_bar_icon = ":src/assets/event/event_bar_icon_light.png"

        # TAB BAR CAMERA SCREEN
        close_tab = ':src/assets/tab_icon/close_tab_light.svg'
        close_filter = ':src/assets/images/close_filter_light.svg'

        add_tab = ":src/assets/images/add_tab_light.svg"
        add_button_icon = ":src/assets/images/add_tab_dark.svg"

        # LOGIN SCREEN
        user = ":src/assets/login_screen/user_light.svg"
        lock = ":src/assets/login_screen/lock_light.svg"
        search_server = ":src/assets/login_screen/search_server_light.svg"
        icon_server_name = ":src/assets/login_screen/icon_server_name_light.svg"
        icon_state_server_on = ":src/assets/login_screen/icon_state_server_on_light.svg"
        icon_state_server_off = ":src/assets/login_screen/icon_state_server_off_light.svg"
        # IMAGES
        no_data_image = ":src/assets/images/no_data_image_light.svg"
        draw_polygon_option = ":src/assets/images/polygon_option_light.svg"
        upload_file = ":src/assets/images/upload_light.svg"
        download_file = ":src/assets/images/download_light.svg"
        down_arrow = "qrc:src/assets/images/down_arrow_light.svg"
        up_arrow = "qrc:src/assets/images/up_arrow_light.svg"
        down_arrow_linedit = ":src/assets/arrows/down_arrow_lineedit.svg"

        # TOOL ICONS
        checkbox_ver2_checked = ":src/assets/tool_icons/checkbox_ver2_checked_light.svg"
        checkbox_ver2_unchecked = ":src/assets/tool_icons/checkbox_ver2_unchecked_light.svg"
        checkbox_ver2_checked_non_qml = ":src/assets/tool_icons/checkbox_ver2_checked_light.svg"
        checkbox_ver2_unchecked_non_qml = ":src/assets/tool_icons/checkbox_ver2_unchecked_light.svg"

        # THEME ICONS
        dark_mode_theme = ":src/assets/tool_icons/dark_mode_theme.png"
        light_mode_theme = ":src/assets/tool_icons/light_mode_theme.png"
        system_mode_theme = ":src/assets/tool_icons/system_mode_theme.png"

        # CAMERA STREAM PTZ ICON
        camera_item = "qrc:src/assets/camera_stream/camera_item_light.svg"
        building_item = "qrc:src/assets/camera_stream/building_item_light.svg"
        icon_volume = ":src/assets/camera_stream/icon_volume_light.svg"
        icon_ptz_off = ":src/assets/camera_stream/icon_ptz_off_light.svg"
        expand_camera = "qrc:src/assets/camera_stream/expand_camera_light.svg"
        shrink_camera = ":src/assets/camera_stream/shrink_camera_light.svg"
        icon_record = ":src/assets/camera_stream/icon_record_light.svg"
        # icon_close = ":src/assets/camera_stream/icon_close_light.svg"
        icon_crop_off = ":src/assets/camera_stream/icon_crop_off_light.svg"
        icon_drag_zoom_off = ":src/assets/camera_stream/icon_drag_zoom_off_light.svg"
        ptz_arrow = ":src/assets/camera_stream/ptz_arrow_light.svg"
        icon_ptz_arrow_off = ":src/assets/camera_stream/icon_ptz_arrow_off_light.svg"
        rotate_camera = "qrc:src/assets/camera_stream/rotate_camera_light.svg"

        # PTZ ICON
        left_top = ":src/assets/ptz_icon/left_top_light.svg"
        top = ":src/assets/ptz_icon/top_light.svg"
        right_top = ":src/assets/ptz_icon/right_top_light.svg"
        left = ":src/assets/ptz_icon/left_light.svg"
        around = ":src/assets/ptz_icon/around_light.svg"
        right = ":src/assets/ptz_icon/right_light.svg"
        left_bottom = ":src/assets/ptz_icon/left_bottom_light.svg"
        bottom = ":src/assets/ptz_icon/bottom_light.svg"
        right_bottom = ":src/assets/ptz_icon/right_bottom_light.svg"
        zoom_in = ":src/assets/ptz_icon/zoom_in_light.svg"
        focus_near = ":src/assets/ptz_icon/focus_near_light.svg"
        iris_add = ":src/assets/ptz_icon/iris_plus_light.svg"
        zoom_out = ":src/assets/ptz_icon/zoom_out_light.svg"
        focus_far = ":src/assets/ptz_icon/focus_far_light.svg"
        iris_not_add = ":src/assets/ptz_icon/iris_minus_light.svg"
        drop_dow = ":src/assets/ptz_icon/dropdown_light.svg"
        drop_dow_right = ":src/assets/ptz_icon/drop_down_right_light.svg"
        down_speed = ":src/assets/ptz_icon/down_speed_light.svg"
        up_speed = ":src/assets/ptz_icon/up_speed_light.svg"

        # PRESET PATRON
        call_preset = ":src/assets/preset_patrol/call_preset_light.svg"
        setting_preset = ":src/assets/preset_patrol/setting_preset_light.svg"
        delete_preset = ":src/assets/preset_patrol/delete_preset_light.svg"
        play_patrol = ":src/assets/preset_patrol/play_patrol_light.svg"
        stop_patrol = ":src/assets/preset_patrol/stop_patrol_light.svg"
        preset = ":src/assets/preset_patrol/preset_light.svg"
        patrol = ":src/assets/preset_patrol/patrol_light.svg"
        pattern = ":src/assets/preset_patrol/pattern_light.svg"
        ptz_advance_brightness = ":src/assets/preset_patrol/ptz_advance_brightness_light.svg"
        ptz_advance_contrast = ":src/assets/preset_patrol/ptz_advance_contrast_light.svg"
        ptz_advance_sharpness = ":src/assets/preset_patrol/ptz_advance_sharpness_light.svg"
        ptz_advance_saturation = ":src/assets/preset_patrol/ptz_advance_saturation_light.svg"
        ptz_advance_menu = ":src/assets/preset_patrol/ptz_advance_menu_light.svg"

        # SPINBOX
        down_spinbox_temp = "qrc:src/assets/tool_icons/down_spinbox_light.svg"
        up_spinbox_temp = "qrc:src/assets/tool_icons/up_spinbox_light.svg"

        # PLAYBACK
        next_chunk = "qrc:src/assets/playback/next_chunk_light.svg"
        next_frame = "qrc:src/assets/playback/next_frame_light.svg"
        new_pause= "qrc:src/assets/playback/pause_light.svg"
        new_play = "qrc:src/assets/playback/play_light.svg"
        previous_chunk = "qrc:src/assets/playback/previous_chunk_light.svg"
        previous_frame = "qrc:src/assets/playback/previous_frame_light.svg"

        # CONTEXT MENU
                # CONTEXT MENU
        icon_ai_flow = "qrc:src/assets/context_menu/icon_ai_flow_light.svg"
        icon_full_screen = "qrc:src/assets/context_menu/icon_full_screen_light.svg"
        icon_open_cam = "qrc:src/assets/context_menu/icon_open_cam_light.svg"
        icon_setting = "qrc:src/assets/context_menu/icon_setting_light.svg"
        icon_video_stream = "qrc:src/assets/context_menu/icon_video_stream_light.svg"
        icon_remove_cam = "qrc:src/assets/context_menu/icon_remove_cam_light.svg"
        ##############################
        cursor = ":src/assets/images/cursor_arrow.cur"
        cursor_busy = ":src/assets/images/cursor_busy.gif"
        cursor_horizontal_resize = ":src/assets/images/cursor_horizontal.cur"
        cursor_vertical_resize = ":src/assets/images/cursor_vertical.cur"
        cursor_top_left_corner = ":src/assets/images/cursor_tlbr.cur"
        cursor_top_right_corner = ":src/assets/images/cursor_bltr.cur"
        cursor_link = ":src/assets/images/cursor_link.cur"
        cursor_help = ":src/assets/images/cursor_help.cur"
        cursor_move = ":src/assets/images/cursor_move.cur"
        add = ":/src/assets/images/plus.svg"

        add_camera_address = ":/src/assets/images/add_camera_address.png"
        delete_icon = ":/src/assets/images/delete_icon.svg"
        delete_icon_disable = ":/src/assets/images/delete_icon_disable.svg"
        delete_camera_address = ":/src/assets/images/delete_camera_address.png"
        delete_ai_script = ":src/assets/images/delete_ai_script.svg"
        show_hide = ":/src/assets/images/show_hide.png"
        close_stream = ":/src/assets/images/close_stream.png"
        sucess_result = ":/src/assets/images/sucess_result.png"
        fail_result = ":/src/assets/images/fail_result.png"
        info_result = ":/src/assets/images/info_result.png"
        close_notify = ":/src/assets/images/close_notify.png"
        edit_virtual = ":/src/assets/images/edit_virtual.png"
        remove_all_virtual = ":/src/assets/images/remove_all_virtual.png"
        add_virtual = ":/src/assets/images/add_virtual.png"
        icon64 = ":/src/assets/images/icon_64.png"
        icon48 = ":/src/assets/images/icon_48.png"
        icon32 = ":/src/assets/images/icon_32.png"
        icon128 = ":/src/assets/images/icon_128.png"
        restart_path = ":/src/assets/images/restart.png"

        ic_camera_connecting = ":src/assets/state/camera_connecting.png"
        ic_camera_disconnect = ":src/assets/state/camera_disconnect.svg"
        pause_path = ":src/assets/images/camera_pause.png"

        server = ":/src/assets/images/server.png"

        fast_forward = ":/src/assets/images/fast_forward.png"
        fast_backward = ":/src/assets/images/fast_backward.png"
        play_video = ":/src/assets/images/play_video.png"
        stop_video = ":/src/assets/images/stop_video.png"
        pause = ":/src/assets/images/pause.png"
        play = ":/src/assets/images/play.png"
        speed_video = ":/src/assets/images/speed_video.png"
        sound_video = ":/src/assets/images/sound_video.png"
        mute_sound_video = ":/src/assets/images/mute_sound_video.png"
        minimize_video = ":/src/assets/images/minimize_video.png"
        fullscreen_video = ":/src/assets/images/fullscreen_video.png"
        volume_down_fill = ":/src/assets/images/volume_down_fill.svg"
        volume_mute_fill = ":/src/assets/images/volume_mute_fill.svg"
        volume_up_fill = ":/src/assets/images/volume_up_fill.svg"

        edit = ":src/assets/images/edit.png"
        double_right = ":src/assets/images/double_right.png"
        plus = ":src/common/widget/feature_controller/images/plus.svg"
        record = ":src/assets/images/record.png"
        edit_map = ":src/assets/map/edit_map.png"
        rotate_map = ":src/assets/map/rotate_map.png"
        zoomin_map = ":src/assets/map/zoomin_map.png"
        zoomout_map = ":src/assets/map/zoomout_map.png"
        
        expand_camera_on_map = "qrc:src/assets/map/expand_stream_light.svg"
        collapse_camera_on_map = "qrc:src/assets/map/collapse_stream_light.svg"
        close_camera_on_map = "qrc:src/assets/map/close_stream_light.svg"

        setting = ":src/assets/images/setting.png"
        show = ":src/assets/images/show.png"
        expand_right = ":src/assets/images/expand_right.svg"
        expand_bottom = ":src/assets/images/expand_bottom.svg"
        audio = ":src/assets/images/audio.png"
        ptz = ":src/assets/images/ptz.png"
        playback = ":src/assets/images/playback.png"
        zoom_digital = ":src/assets/images/zoom_digital.png"
        stop_live = ":src/assets/images/stop_live.png"
        info = ":src/assets/images/info.png"
        icon_status_timeline = ":src/assets/splitter/icon_status_timeline_light.svg"
        calendar_icon = ":/src/assets/images/icon_calendar.svg"

        vehicle_detection_on = ":src/assets/ai_icons/vehicle_detection_on_light.svg"
        vehicle_detection_ver2 = ":src/assets/ai_icons/vehicle_detection_ver2.svg"
        vehicle_detection_off = ":src/assets/ai_icons/vehicle_detection_off.svg"
        face_recognition_on = ":src/assets/ai_icons/face_recognition_on_light.svg"
        face_recognition_off = ":src/assets/ai_icons/face_recognition_off.svg"
        face_recognition_ver2 = ":src/assets/ai_icons/face_recognition_ver2.svg"
        crowd_detection_on = ":src/assets/ai_icons/crowd_detection_on_light.svg"
        crowd_detection_off = ":src/assets/ai_icons/crowd_detection_off.svg"
        access_control_on = ":src/assets/ai_icons/access_control_on_light.svg"
        access_control_off = ":src/assets/ai_icons/access_control_off.svg"

        ic_recognition_security = ":src/assets/ai_icons/ic_recognition_security.svg"
        ic_risk_identification = ":src/assets/ai_icons/ic_risk_identification.svg"

        apply = ":src/assets/images/apply.png"
        speed = ":src/assets/images/speed.png"
        share = ":src/assets/images/share.png"
        cut = ":src/assets/images/cut.png"
        recording_icon_header = ":src/assets/images/recording.png"
        icon_volume = ":src/assets/camera_stream/icon_volume.svg"
        icon_ptz_on = ":src/assets/camera_stream/icon_ptz_on.svg"
        icon_ptz_off = ":src/assets/camera_stream/icon_ptz_off.svg"
        # expand_camera = ":src/assets/camera_stream/expand_camera.svg"
        shrink_camera = "qrc:src/assets/camera_stream/shrink_camera_light.svg"
        icon_record = ":src/assets/camera_stream/icon_record.svg"
        icon_close = "qrc:src/assets/camera_stream/icon_close_light.svg"
        icon_crop_off = ":src/assets/camera_stream/icon_crop_off.svg"
        icon_crop_on = ":src/assets/camera_stream/icon_crop_on.svg"
        icon_drag_zoom_off = ":src/assets/camera_stream/icon_drag_zoom_off.svg"
        icon_drag_zoom = "qrc:src/assets/camera_stream/icon_drag_zoom_light.svg"
        icon_drag_zoom_on = ":src/assets/camera_stream/icon_drag_zoom_on.svg"
        ptz_arrow = ":src/assets/camera_stream/ptz_arrow.svg"
        icon_ptz_arrow_off = ":src/assets/camera_stream/icon_ptz_arrow_off.svg"
        icon_ptz_arrow_on = ":src/assets/camera_stream/icon_ptz_arrow_on.svg"
        icon_ptz_arrow = "qrc:src/assets/camera_stream/icon_ptz_arrow_light.svg"
        icon_ptz = "qrc:src/assets/camera_stream/icon_ptz_light.svg"
        icon_search_filter = "qrc:src/assets/camera_stream/icon_search_filter_light.svg"

        # lightning_on = ":src/assets/event/lightning_on.svg"
        # lightning_off = ":src/assets/event/lightning_off.svg"
        # alert_off = ":src/assets/event/alert_off.svg"
        # alert_on = ":src/assets/event/alert_on.svg"
        # calendar = ":src/assets/event/calendar.svg"

        playback_tab_select = ":src/assets/side_menu_icon/playback_screen_on.svg"
        map_tab_select = ":src/assets/side_menu_icon/map_screen_on.svg"
        playback_tab_not_select = ":src/assets/side_menu_icon/playback_screen_off.svg"
        map_tab_not_select = ":src/assets/side_menu_icon/map_screen_off.svg"
        logo_tab_bar = ":src/assets/side_menu_icon/logo_tab_bar.svg"

        mic_disable = ":src/assets/images/mic_disable.png"
        record_off = ":src/assets/images/record_off.png"
        record_disable = ":src/assets/images/record_disable.png"
        record_on = ":src/assets/images/record_on.png"
        capture_disable = ":src/assets/images/capture_disable.png"
        capture_off = ":src/assets/images/capture_off.png"
        fullscreen_off = ":src/assets/images/fullscreen_off.png"
        volume_disable = ":src/assets/images/volume_disable.png"
        volume_off = ":src/assets/images/volume_off.png"
        volume_on = ":src/assets/images/volume_on.png"
        window_size_off = ":src/assets/images/window_size_off.png"
        # close_tab = ":src/assets/images/close_tab.png"
        colume_option = ":src/assets/images/colume_option.png"
        state_online = ":src/assets/state/state_online.svg"
        state_offline = ":src/assets/state/state_offline.svg"

        # tool icons
        checkbox_unchecked_white = ":src/assets/tool_icons/checkbox_unchecked_white.svg"
        trash = ":src/assets/tool_icons/trash.svg"
        trash_ver2 = ":src/assets/tool_icons/trash_ver2.svg"
        trash_mini = ":src/assets/tool_icons/mini_trash.svg"
        pen = ":src/assets/tool_icons/edit.svg"
        pen_ver2 = ":src/assets/tool_icons/pen_ver2.svg"

        # tree view icon, menu in treeview icon
        camera_active_icon_red = ":src/assets/treeview_and_menu_treeview/camera_active_icon_red.svg"
        camera_active_icon_green = ":src/assets/treeview_and_menu_treeview/camera_active_icon_green.svg"
        camera_active_icon_green_qml = "qrc:/src/assets/treeview_and_menu_treeview/camera_active_icon_green.svg"
        norec_pin = ":src/assets/treeview_and_menu_treeview/norec_pin.svg"
        connected_norec_unpin = ":src/assets/treeview_and_menu_treeview/connected_norec_unpin.svg"
        rec_pin = ":src/assets/treeview_and_menu_treeview/rec_pin.svg"
        rec_unpin = ":src/assets/treeview_and_menu_treeview/rec_unpin.svg"
        norec_pin = ":src/assets/treeview_and_menu_treeview/norec_pin.svg"
        disconnected_norec_unpin = ":src/assets/treeview_and_menu_treeview/disconnected_norec_unpin.svg"
        rec_pin = ":src/assets/treeview_and_menu_treeview/rec_pin.svg"
        disrec_unpin = ":src/assets/treeview_and_menu_treeview/disrec_unpin.svg"
        norec_pin_qml = "qrc:/src/assets/treeview_and_menu_treeview/norec_pin.svg"
        connected_norec_unpin_qml = "qrc:/src/assets/treeview_and_menu_treeview/connected_norec_unpin.svg"
        rec_pin_qml = "qrc:/src/assets/treeview_and_menu_treeview/rec_pin.svg"
        rec_unpin_qml = "qrc:/src/assets/treeview_and_menu_treeview/rec_unpin.svg"
        norec_pin_qml = "qrc:/src/assets/treeview_and_menu_treeview/norec_pin.svg"
        disconnected_norec_unpin_qml = "qrc:/src/assets/treeview_and_menu_treeview/disconnected_norec_unpin.svg"
        rec_pin_qml = "qrc:/src/assets/treeview_and_menu_treeview/rec_pin.svg"
        disrec_unpin_qml = "qrc:/src/assets/treeview_and_menu_treeview/disrec_unpin.svg"
        camera_inactive_icon = ":src/assets/treeview_and_menu_treeview/camera_in_active_treeview.svg"
        choose_position_icon = ":src/assets/treeview_and_menu_treeview/choose_position_camera_treeview.png"
        exit_stream_treeview = ":src/assets/treeview_and_menu_treeview/exit_stream_treeview.png"
        # group_camera_treeview = ":src/assets/treeview_and_menu_treeview/group_camera_treeview.svg"
        # open_in_tab = ":src/assets/treeview_and_menu_treeview/open_in_tab_treeview.png"
        # treeview_server = ":src/assets/treeview_and_menu_treeview/treeview_server.svg"
        # list_devices = ":src/assets/treeview_and_menu_treeview/list_devices.svg"
        # list_map = ":src/assets/treeview_and_menu_treeview/list_map.svg"
        # list_virtual_window = ":src/assets/treeview_and_menu_treeview/list_virtual_windows.svg"
        # list_save_view = ":src/assets/treeview_and_menu_treeview/list_save_view.svg"
        # close_all_virtual = ":/src/assets/treeview_and_menu_treeview/close_all_virtual.svg"
        # open_all_virtual = ":/src/assets/treeview_and_menu_treeview/open_all_virtual.svg"
        previous_first = ":src/assets/images/first_page.svg"
        previous = ":src/assets/images/previous_page.svg"
        next = ":src/assets/images/next_page.svg"
        next_last = ":src/assets/images/latest_page.svg"
        expand_item_treeview = ":src/assets/treeview_and_menu_treeview/expand_treeview.svg"
        collapse_item_treeview = ":src/assets/treeview_and_menu_treeview/collapse_treeview.svg"
        new_search = ":src/assets/treeview_and_menu_treeview/new_search.svg"
        Filter = ":src/assets/treeview_and_menu_treeview/Filter.png"
        Change_mode = ":src/assets/treeview_and_menu_treeview/change_mode.svg"
        ai_menu_filter = ":src/assets/treeview_and_menu_treeview/ai_menu_filter.png"
        complex_tree = ":src/assets/treeview_and_menu_treeview/complex_tree.png"
        state_menu_filter = ":src/assets/treeview_and_menu_treeview/state_menu_filter.png"

        # PTZ icon
        # left_top = ":src/assets/ptz_icon/left_top.svg"
        # top = ":src/assets/ptz_icon/top.svg"
        # right_top = ":src/assets/ptz_icon/right_top.svg"
        # left = ":src/assets/ptz_icon/left.svg"
        # around = ":src/assets/ptz_icon/around.svg"
        # right = ":src/assets/ptz_icon/right.svg"
        # left_bottom = ":src/assets/ptz_icon/left_bottom.svg"
        # bottom = ":src/assets/ptz_icon/bottom.svg"
        # right_bottom = ":src/assets/ptz_icon/right_bottom.svg"
        # zoom_in = ":src/assets/ptz_icon/zoom_in.svg"
        # focus_near = ":src/assets/ptz_icon/focus_near.svg"
        # iris_add = ":src/assets/ptz_icon/iris_plus.svg"
        # zoom_out = ":src/assets/ptz_icon/zoom_out.svg"
        # focus_far = ":src/assets/ptz_icon/focus_far.svg"
        # iris_not_add = ":src/assets/ptz_icon/iris_minus.svg"
        # drop_dow = ":src/assets/ptz_icon/dropdown.svg"
        # drop_dow_right = ":src/assets/ptz_icon/drop_down_right.svg"
        close_dialog = ":src/assets/images/close_dialog.png"
        # Login screen
        frame_login = ":src/assets/login_screen/frame_login.png"
        login_background = ":src/assets/login_screen/login_background.png"
        add_server = ":src/assets/login_screen/add_server_light.svg"
        noti_server = ":src/assets/login_screen/noti_server_light.svg"
        logo = ":src/assets/login_screen/logo.svg"
        logo_video = ":src/assets/login_screen/login_video.gif"
        logovideobackground = ":src/assets/login_screen/loginvideobackground.gif"
        edit_server = ":src/assets/login_screen/edit_server.png"
        edit_server = ":src/assets/login_screen/edit_server.png"
        eye_close = ":src/assets/login_screen/eye_close.png"
        eye = ":src/assets/login_screen/eye.png"
        # preset --------------
        # call_preset = ":src/assets/preset_patrol/call_preset.svg"
        # setting_preset = ":src/assets/preset_patrol/setting_preset.svg"
        # delete_preset = ":src/assets/preset_patrol/delete_preset.svg"
        # play_patrol = ":src/assets/preset_patrol/play_patrol.svg"
        # stop_patrol = ":src/assets/preset_patrol/stop_patrol.svg"
        # preset = ":src/assets/preset_patrol/preset.svg"
        # patrol = ":src/assets/preset_patrol/patrol.svg"
        # pattern = ":src/assets/preset_patrol/pattern.svg"
        # add_preset = ":src/assets/preset_patrol/add_preset.svg"
        # delete_preset_patrol = ":src/assets/preset_patrol/delete_preset_patrol.svg"
        # down_preset = ":src/assets/preset_patrol/down_preset.svg"
        # up_preset = ":src/assets/preset_patrol/up_preset.svg"
        # call_preset_hover = ":src/assets/preset_patrol/call_preset_hover.svg"
        # setting_preset_hover = ":src/assets/preset_patrol/setting_preset_hover.svg"
        # delete_preset_hover = ":src/assets/preset_patrol/delete_preset_hover.svg"
        # play_patrol_hover = ":src/assets/preset_patrol/play_patrol_hover.svg"
        # stop_patrol_hover = ":src/assets/preset_patrol/stop_patrol_hover.svg"
        # ptz_advance_brightness = ":src/assets/preset_patrol/ptz_advance_brightness.svg"
        # ptz_advance_contrast = ":src/assets/preset_patrol/ptz_advance_contrast.svg"
        # ptz_advance_sharpness = ":src/assets/preset_patrol/ptz_advance_sharpness.svg"
        # ptz_advance_saturation = ":src/assets/preset_patrol/ptz_advance_saturation.svg"
        # ptz_advance_menu = ":src/assets/preset_patrol/ptz_advance_menu.svg"
        #
        # down_speed = ":src/assets/ptz_icon/down_speed.svg"
        # up_speed = ":src/assets/ptz_icon/up_speed.svg"
        # tab
        icon_active = ":src/assets/tab_icon/icon_active.png"
        exit_fullscreen = ":src/assets/images/exit_fullscreen.png"
        exit_fullscreen1 = ":src/assets/images/exit_fullscreen1.png"
        # draw detect area
        draw_pencil = ":src/assets/images/draw_pencil.png"
        eraser = ":src/assets/images/eraser.png"
        draw_line_option = ":src/assets/images/line_option.svg"
        down_arrow_custom = ":src/assets/images/down_arrow_custom.png"
        # right bar: filter event, time filter
        # ic_filter = ":src/assets/event/filter.svg"
        ic_time_filter = ":src/assets/images/time_filter.png"
        play_alarm_sound = ":src/assets/images/play_sound.svg"
        disable_play_alarm_sound = ":src/assets/images/disable_play_alarm_sound.svg"
        warning = ":src/assets/images/warning.gif"
        close_alert = ":src/assets/images/close_alert.png"
        alert_event = ":src/assets/images/alert_event.png"
        state_read_warning = ":src/assets/images/state_read.svg"
        # Playback screen
        download_playback = ":src/assets/playback/download_playback.png"
        mute_fill_playback = ":src/assets/playback/mute_fill.png"
        next_fill_playback = ":src/assets/playback/next_fill.png"
        play_fill_playback = ":src/assets/playback/play_fill.png"
        previous_fill_playback = ":src/assets/playback/previous_fill.png"
        volume_up_playback = ":src/assets/playback/volume_up.png"
        pause_fill_playback = ":src/assets/playback/pause_fill_playback.png"
        edit_zones = ":src/assets/images/edit_zones.png"
        ic_map = ":src/assets/treeview_and_menu_treeview/map_item.svg"
        delete_camera_on_map = ":src/assets/images/delete_camera_on_map.png"
        radio_button_checked = ":src/assets/images/radio_button_checked.png"
        radio_button_uncheck = ":src/assets/images/radio_button_uncheck.png"
        ic_ptz = ":src/assets/images/ic_ptz.png"
        ic_dome = ":src/assets/images/ic_dome.png"
        ic_bullet = ":src/assets/images/ic_bullet.png"
        ic_ptz_disable = ":src/assets/images/ic_ptz_disable.png"
        ic_dome_disable = ":src/assets/images/ic_dome_disable.png"
        ic_bullet_disable = ":src/assets/images/ic_bullet_disable.png"
        ic_image_not_found = ":src/assets/images/image_not_found.png"

        edit_map_name = ":src/assets/images/edit_map_name.png"
        edit_map_name_enable = ":src/assets/images/edit_map_name_enable.png"
        edit_map_camera = ":src/assets/images/edit_map_camera.png"
        edit_map_camera_enable = ":src/assets/images/edit_map_camera_enable.png"
        ahihi = ":src/assets/images/ahihi.jpg"
        face = ":src/assets/images/face.jpg"
        ic_bullet_svg = ":src/assets/images/ic_bullet.svg"
        ic_dome_svg = ":src/assets/images/ic_dome.svg"
        ic_ptz_svg = ":src/assets/images/ic_ptz.svg"
        # # Side Menu active icon
        # icon_sidebar_big_left = ":src/assets/side_menu_icon/chevron_big_left.svg"
        # icon_status_sidebar = ":src/assets/side_menu_icon/icon_status_sidebar.svg"
        # icon_sidebar_big_right = ":src/assets/side_menu_icon/chevron_big_right.svg"

        # title bar
        close_application = ":src/assets/title_bar/close_window.svg"
        minimize_window = ":src/assets/title_bar/minimize_window.svg"
        maximize_window = ":src/assets/title_bar/maximize_window.svg"
        settings_application = ":src/assets/title_bar/settings.svg"
        normal_window = ":/src/assets/title_bar/normal_window.svg"
        close_application_light = ":src/assets/title_bar/close_window_light.svg"
        minimize_window_light = ":src/assets/title_bar/minimize_window_light.svg"
        maximize_window_light = ":src/assets/title_bar/maximize_window_light.svg"
        settings_application_light = ":src/assets/title_bar/settings_light.svg"
        normal_window_light = ":/src/assets/title_bar/normal_window_light.svg"

        ic_zone_activate = ":/src/assets/images/ic_zone_activate.png"
        edit_script = ":/src/assets/images/edit_script.png"
        alarm_alert_icon = ":/src/assets/images/alarm_alert.svg"
        camera_background_image = ":/src/assets/images/camera_background_image.svg"
        down_arrow_spinbox = ":src/assets/arrows/down_arrow_spinbox.svg"
        up_arrow_spinbox = ":src/assets/arrows/up_arrow_spinbox.svg"
        arrow_combobox = ":src/assets/arrows/arrow_combobox.svg"
        # Table
        eye_in_table = ":/src/assets/images/eye_in_table.svg"
        edit_in_table = ":/src/assets/images/edit_in_table.svg"
        # Dialog
        add_avatar = ":/src/assets/images/add_avatar.svg"
        clear_input = ":src/assets/images/clear_input.svg"
        loading_image = ":src/assets/images/loading_image.png"

        # Time picker icons
        time_picker_up = ":src/assets/playback/time_picker_up_light.png"
        time_picker_down = ":src/assets/playback/time_picker_down_light.png"
        search_not_found = ":src/assets/images/search_not_found_light.svg"
        search_not_found_qml = "qrc:src/assets/images/search_not_found_light.svg"

        # Device tab icons
        edit_device_item = "qrc:src/common/qml/device_table/images/pen_ver2_light.svg"
        delete_device_item = "qrc:src/common/qml/device_table/images/trash_ver2_light.svg"

    class Menu:
        stop = "src/assets/menu_icon/StopCircle.png"
        camera = "src/assets/menu_icon/Camera.png"
        rec = "src/assets/menu_icon/REC.png"
        ptz_control2 = "src/assets/menu_icon/PTZcontrol2.png"
        flag = "src/assets/menu_icon/Flag.png"
        playback = "src/assets/menu_icon/Playback.png"
        volume = "src/assets/menu_icon/VolumeUp.png"
        slider = "src/assets/menu_icon/Sliders.png"
        mic = "src/assets/menu_icon/Mic.png"
        ic_full_screen = "src/assets/menu_icon/ic_full_screen.png"
        close = "src/assets/menu_icon/Close.png"
