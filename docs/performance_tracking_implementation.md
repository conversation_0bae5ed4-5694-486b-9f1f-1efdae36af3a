# Triển khai Performance Tracking cho iVMS sử dụng Thư viện có sẵn

## Tổng quan

Tài liệu này mô tả cách triển khai hệ thống performance tracking cho iVMS sử dụng các thư viện Python có sẵn, đ<PERSON><PERSON> bả<PERSON> hi<PERSON> quả, <PERSON><PERSON> định và không ảnh hưởng đến hiệu năng ứng dụng.

## Kiến trúc Tổng thể

### Nguyên tắc Thiết kế

1. **Layered Architecture** - Phân tầng rõ ràng giữa các loại monitoring
2. **Minimal Overhead** - Sử dụng thư viện được optimize
3. **Production-Safe** - An toàn cho môi trường production
4. **Configurable** - C<PERSON> thể bật/tắt và cấu hình linh hoạt
5. **Non-Intrusive** - Không modify existing code

### Cấu trúc <PERSON>ệ thống

```
Performance Monitoring System
├── Core Monitoring (psutil-based)
│   ├── System Metrics Collector
│   ├── Process Metrics Collector
│   └── Thread Metrics Collector
├── External Profiling (py-spy)
│   ├── Real-time Profiling
│   ├── Flame Graph Generation
│   └── Call Stack Analysis
├── Memory Tracking (tracemalloc + pympler)
│   ├── Memory Usage Monitoring
│   ├── Leak Detection
│   └── Object Tracking
└── Development Tools (line_profiler, memory_profiler)
    ├── Line-by-line Profiling
    └── Function-level Analysis
```

## Chi tiết Triển khai

### 1. Core Monitoring với psutil

#### A. System Metrics Collector

```python
import psutil
import threading
import time
from collections import deque
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime

@dataclass
class SystemMetrics:
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float

@dataclass
class ThreadMetrics:
    thread_id: int
    thread_name: str
    user_time: float
    system_time: float
    total_time: float

@dataclass
class ProcessMetrics:
    timestamp: float
    pid: int
    cpu_percent: float
    memory_rss_mb: float
    memory_vms_mb: float
    num_threads: int
    num_fds: int
    threads: List[ThreadMetrics]

class PSUtilMonitor:
    """Core monitoring system sử dụng psutil"""
    
    def __init__(self, sample_interval: float = 1.0, max_samples: int = 3600):
        self.sample_interval = sample_interval
        self.max_samples = max_samples
        
        # Data storage
        self.system_metrics = deque(maxlen=max_samples)
        self.process_metrics = deque(maxlen=max_samples)
        
        # Monitoring state
        self.is_monitoring = False
        self.monitor_thread = None
        self.stop_event = threading.Event()
        
        # Process reference
        self.process = psutil.Process()
        
        # Previous values for delta calculations
        self._prev_disk_io = None
        self._prev_network_io = None
        
    def start_monitoring(self):
        """Bắt đầu monitoring"""
        if self.is_monitoring:
            return
            
        self.is_monitoring = True
        self.stop_event.clear()
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True,
            name="PSUtilMonitor"
        )
        self.monitor_thread.start()
        print("[PSUtilMonitor] Started monitoring")
        
    def stop_monitoring(self):
        """Dừng monitoring"""
        if not self.is_monitoring:
            return
            
        self.is_monitoring = False
        self.stop_event.set()
        
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
            self.monitor_thread = None
            
        print("[PSUtilMonitor] Stopped monitoring")
        
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while not self.stop_event.wait(self.sample_interval):
            try:
                # Collect system metrics
                system_metrics = self._collect_system_metrics()
                if system_metrics:
                    self.system_metrics.append(system_metrics)
                
                # Collect process metrics
                process_metrics = self._collect_process_metrics()
                if process_metrics:
                    self.process_metrics.append(process_metrics)
                    
            except Exception as e:
                print(f"[PSUtilMonitor] Error in monitoring loop: {e}")
                
    def _collect_system_metrics(self) -> Optional[SystemMetrics]:
        """Thu thập system metrics"""
        try:
            timestamp = time.time()
            
            # CPU and Memory
            cpu_percent = psutil.cpu_percent(interval=None)
            memory = psutil.virtual_memory()
            
            # Disk I/O
            disk_io = psutil.disk_io_counters()
            disk_read_mb = 0
            disk_write_mb = 0
            
            if disk_io and self._prev_disk_io:
                disk_read_mb = (disk_io.read_bytes - self._prev_disk_io.read_bytes) / (1024 * 1024)
                disk_write_mb = (disk_io.write_bytes - self._prev_disk_io.write_bytes) / (1024 * 1024)
            
            self._prev_disk_io = disk_io
            
            # Network I/O
            network_io = psutil.net_io_counters()
            network_sent_mb = 0
            network_recv_mb = 0
            
            if network_io and self._prev_network_io:
                network_sent_mb = (network_io.bytes_sent - self._prev_network_io.bytes_sent) / (1024 * 1024)
                network_recv_mb = (network_io.bytes_recv - self._prev_network_io.bytes_recv) / (1024 * 1024)
                
            self._prev_network_io = network_io
            
            return SystemMetrics(
                timestamp=timestamp,
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / (1024 * 1024),
                memory_available_mb=memory.available / (1024 * 1024),
                disk_io_read_mb=disk_read_mb,
                disk_io_write_mb=disk_write_mb,
                network_sent_mb=network_sent_mb,
                network_recv_mb=network_recv_mb
            )
            
        except Exception as e:
            print(f"[PSUtilMonitor] Error collecting system metrics: {e}")
            return None
            
    def _collect_process_metrics(self) -> Optional[ProcessMetrics]:
        """Thu thập process metrics"""
        try:
            timestamp = time.time()
            
            # Process info
            cpu_percent = self.process.cpu_percent()
            memory_info = self.process.memory_info()
            num_threads = self.process.num_threads()
            
            # File descriptors (Unix only)
            num_fds = 0
            try:
                num_fds = self.process.num_fds()
            except (AttributeError, psutil.AccessDenied):
                pass
            
            # Thread metrics
            thread_metrics = self._collect_thread_metrics()
            
            return ProcessMetrics(
                timestamp=timestamp,
                pid=self.process.pid,
                cpu_percent=cpu_percent,
                memory_rss_mb=memory_info.rss / (1024 * 1024),
                memory_vms_mb=memory_info.vms / (1024 * 1024),
                num_threads=num_threads,
                num_fds=num_fds,
                threads=thread_metrics
            )
            
        except Exception as e:
            print(f"[PSUtilMonitor] Error collecting process metrics: {e}")
            return None
            
    def _collect_thread_metrics(self) -> List[ThreadMetrics]:
        """Thu thập thread metrics"""
        thread_metrics = []
        
        try:
            threads = self.process.threads()
            
            for thread in threads:
                thread_name = self._get_thread_name(thread.id)
                total_time = thread.user_time + thread.system_time
                
                thread_metric = ThreadMetrics(
                    thread_id=thread.id,
                    thread_name=thread_name,
                    user_time=thread.user_time,
                    system_time=thread.system_time,
                    total_time=total_time
                )
                
                thread_metrics.append(thread_metric)
                
        except Exception as e:
            print(f"[PSUtilMonitor] Error collecting thread metrics: {e}")
            
        return thread_metrics
        
    def _get_thread_name(self, thread_id: int) -> str:
        """Lấy tên thread từ ID"""
        # Map thread ID to meaningful names based on iVMS architecture
        thread_names = {
            # Main thread patterns
            # Video player threads
            # Worker threads
            # WebSocket threads
        }
        
        return thread_names.get(thread_id, f"Thread-{thread_id}")
        
    def get_current_metrics(self) -> Dict[str, Any]:
        """Lấy metrics hiện tại"""
        result = {}
        
        if self.system_metrics:
            result['system'] = self.system_metrics[-1]
            
        if self.process_metrics:
            result['process'] = self.process_metrics[-1]
            
        return result
        
    def get_thread_performance_summary(self, duration_minutes: int = 5) -> Dict[str, Any]:
        """Lấy tổng kết performance của threads"""
        if not self.process_metrics:
            return {}
            
        # Get samples from last N minutes
        cutoff_time = time.time() - (duration_minutes * 60)
        recent_samples = [
            sample for sample in self.process_metrics 
            if sample.timestamp >= cutoff_time
        ]
        
        if not recent_samples:
            return {}
            
        # Aggregate thread metrics
        thread_summary = {}
        
        for sample in recent_samples:
            for thread in sample.threads:
                thread_name = thread.thread_name
                
                if thread_name not in thread_summary:
                    thread_summary[thread_name] = {
                        'thread_id': thread.thread_id,
                        'total_cpu_time': 0,
                        'samples': 0,
                        'max_cpu_time': 0,
                        'min_cpu_time': float('inf')
                    }
                
                stats = thread_summary[thread_name]
                stats['total_cpu_time'] += thread.total_time
                stats['samples'] += 1
                stats['max_cpu_time'] = max(stats['max_cpu_time'], thread.total_time)
                stats['min_cpu_time'] = min(stats['min_cpu_time'], thread.total_time)
        
        # Calculate averages
        for thread_name, stats in thread_summary.items():
            if stats['samples'] > 0:
                stats['avg_cpu_time'] = stats['total_cpu_time'] / stats['samples']
                
        return thread_summary
        
    def export_metrics(self, filepath: str, duration_minutes: int = 60):
        """Export metrics ra file"""
        import json
        
        cutoff_time = time.time() - (duration_minutes * 60)
        
        # Filter data
        system_data = [
            {
                'timestamp': m.timestamp,
                'cpu_percent': m.cpu_percent,
                'memory_percent': m.memory_percent,
                'memory_used_mb': m.memory_used_mb,
                'disk_io_read_mb': m.disk_io_read_mb,
                'disk_io_write_mb': m.disk_io_write_mb,
                'network_sent_mb': m.network_sent_mb,
                'network_recv_mb': m.network_recv_mb
            }
            for m in self.system_metrics if m.timestamp >= cutoff_time
        ]
        
        process_data = [
            {
                'timestamp': m.timestamp,
                'pid': m.pid,
                'cpu_percent': m.cpu_percent,
                'memory_rss_mb': m.memory_rss_mb,
                'num_threads': m.num_threads,
                'threads': [
                    {
                        'thread_id': t.thread_id,
                        'thread_name': t.thread_name,
                        'user_time': t.user_time,
                        'system_time': t.system_time,
                        'total_time': t.total_time
                    }
                    for t in m.threads
                ]
            }
            for m in self.process_metrics if m.timestamp >= cutoff_time
        ]
        
        export_data = {
            'export_time': time.time(),
            'duration_minutes': duration_minutes,
            'system_metrics': system_data,
            'process_metrics': process_data,
            'thread_summary': self.get_thread_performance_summary(duration_minutes)
        }
        
        with open(filepath, 'w') as f:
            json.dump(export_data, f, indent=2)
            
        print(f"[PSUtilMonitor] Exported {len(system_data)} system samples and {len(process_data)} process samples to {filepath}")

# Global instance
psutil_monitor = PSUtilMonitor()
```

### 2. Memory Tracking với tracemalloc

#### A. Built-in Memory Tracer

```python
import tracemalloc
import threading
import time
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

@dataclass
class MemorySnapshot:
    timestamp: float
    current_mb: float
    peak_mb: float
    trace_count: int
    top_allocations: List[Tuple[str, float]]  # (location, size_mb)

class TraceMallocMonitor:
    """Memory tracking sử dụng tracemalloc"""
    
    def __init__(self, max_snapshots: int = 100):
        self.max_snapshots = max_snapshots
        self.snapshots = []
        self.is_tracing = False
        self._lock = threading.Lock()
        
    def start_tracing(self):
        """Bắt đầu memory tracing"""
        if self.is_tracing:
            return
            
        tracemalloc.start()
        self.is_tracing = True
        print("[TraceMallocMonitor] Started memory tracing")
        
    def stop_tracing(self):
        """Dừng memory tracing"""
        if not self.is_tracing:
            return
            
        tracemalloc.stop()
        self.is_tracing = False
        print("[TraceMallocMonitor] Stopped memory tracing")
        
    def take_snapshot(self, top_count: int = 10) -> Optional[MemorySnapshot]:
        """Chụp snapshot memory hiện tại"""
        if not self.is_tracing:
            return None
            
        try:
            # Get current memory usage
            current, peak = tracemalloc.get_traced_memory()
            
            # Take snapshot
            snapshot = tracemalloc.take_snapshot()
            top_stats = snapshot.statistics('lineno')
            
            # Get top allocations
            top_allocations = []
            for stat in top_stats[:top_count]:
                location = f"{stat.traceback.format()[-1]}"
                size_mb = stat.size / (1024 * 1024)
                top_allocations.append((location, size_mb))
            
            memory_snapshot = MemorySnapshot(
                timestamp=time.time(),
                current_mb=current / (1024 * 1024),
                peak_mb=peak / (1024 * 1024),
                trace_count=len(top_stats),
                top_allocations=top_allocations
            )
            
            with self._lock:
                self.snapshots.append(memory_snapshot)
                if len(self.snapshots) > self.max_snapshots:
                    self.snapshots.pop(0)
                    
            return memory_snapshot
            
        except Exception as e:
            print(f"[TraceMallocMonitor] Error taking snapshot: {e}")
            return None
            
    def get_memory_growth(self, duration_minutes: int = 10) -> Optional[float]:
        """Tính memory growth rate (MB/minute)"""
        if len(self.snapshots) < 2:
            return None
            
        cutoff_time = time.time() - (duration_minutes * 60)
        recent_snapshots = [
            s for s in self.snapshots if s.timestamp >= cutoff_time
        ]
        
        if len(recent_snapshots) < 2:
            return None
            
        first_snapshot = recent_snapshots[0]
        last_snapshot = recent_snapshots[-1]
        
        time_diff = last_snapshot.timestamp - first_snapshot.timestamp
        memory_diff = last_snapshot.current_mb - first_snapshot.current_mb
        
        if time_diff > 0:
            return (memory_diff / time_diff) * 60  # MB per minute
            
        return None
        
    def detect_memory_leaks(self, threshold_mb_per_minute: float = 1.0) -> bool:
        """Phát hiện memory leaks"""
        growth_rate = self.get_memory_growth()
        
        if growth_rate is None:
            return False
            
        return growth_rate > threshold_mb_per_minute
        
    def get_current_memory_info(self) -> Dict[str, float]:
        """Lấy thông tin memory hiện tại"""
        if not self.is_tracing:
            return {}
            
        try:
            current, peak = tracemalloc.get_traced_memory()
            return {
                'current_mb': current / (1024 * 1024),
                'peak_mb': peak / (1024 * 1024)
            }
        except:
            return {}

# Global instance
tracemalloc_monitor = TraceMallocMonitor()
```

### 3. External Profiling với py-spy

#### A. Py-spy Integration Manager

```python
import subprocess
import os
import tempfile
from typing import Optional, Dict, Any
from pathlib import Path

class PySpyManager:
    """Manager cho py-spy external profiling"""

    def __init__(self):
        self.current_pid = os.getpid()
        self.output_dir = Path("performance_profiles")
        self.output_dir.mkdir(exist_ok=True)

    def is_available(self) -> bool:
        """Kiểm tra py-spy có available không"""
        try:
            result = subprocess.run(['py-spy', '--version'],
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False

    def start_top_monitoring(self, duration: int = 60) -> Optional[subprocess.Popen]:
        """Bắt đầu real-time monitoring với py-spy top"""
        if not self.is_available():
            print("[PySpyManager] py-spy not available")
            return None

        try:
            cmd = [
                'py-spy', 'top',
                '--pid', str(self.current_pid),
                '--duration', str(duration)
            ]

            process = subprocess.Popen(cmd, stdout=subprocess.PIPE,
                                     stderr=subprocess.PIPE, text=True)
            print(f"[PySpyManager] Started py-spy top monitoring for {duration}s")
            return process

        except Exception as e:
            print(f"[PySpyManager] Error starting py-spy top: {e}")
            return None

    def record_flame_graph(self, duration: int = 60,
                          output_name: Optional[str] = None) -> Optional[str]:
        """Record flame graph"""
        if not self.is_available():
            print("[PySpyManager] py-spy not available")
            return None

        if output_name is None:
            timestamp = int(time.time())
            output_name = f"flame_graph_{timestamp}.svg"

        output_path = self.output_dir / output_name

        try:
            cmd = [
                'py-spy', 'record',
                '-o', str(output_path),
                '--pid', str(self.current_pid),
                '--duration', str(duration),
                '--format', 'svg'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=duration + 10)

            if result.returncode == 0:
                print(f"[PySpyManager] Flame graph saved to {output_path}")
                return str(output_path)
            else:
                print(f"[PySpyManager] Error recording flame graph: {result.stderr}")
                return None

        except Exception as e:
            print(f"[PySpyManager] Error recording flame graph: {e}")
            return None

    def dump_call_stacks(self, include_locals: bool = False) -> Optional[str]:
        """Dump current call stacks"""
        if not self.is_available():
            return None

        try:
            cmd = ['py-spy', 'dump', '--pid', str(self.current_pid)]

            if include_locals:
                cmd.append('--locals')

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                return result.stdout
            else:
                print(f"[PySpyManager] Error dumping call stacks: {result.stderr}")
                return None

        except Exception as e:
            print(f"[PySpyManager] Error dumping call stacks: {e}")
            return None

    def record_speedscope_profile(self, duration: int = 60,
                                 output_name: Optional[str] = None) -> Optional[str]:
        """Record speedscope profile"""
        if not self.is_available():
            return None

        if output_name is None:
            timestamp = int(time.time())
            output_name = f"speedscope_{timestamp}.json"

        output_path = self.output_dir / output_name

        try:
            cmd = [
                'py-spy', 'record',
                '-o', str(output_path),
                '--pid', str(self.current_pid),
                '--duration', str(duration),
                '--format', 'speedscope'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=duration + 10)

            if result.returncode == 0:
                print(f"[PySpyManager] Speedscope profile saved to {output_path}")
                return str(output_path)
            else:
                print(f"[PySpyManager] Error recording speedscope profile: {result.stderr}")
                return None

        except Exception as e:
            print(f"[PySpyManager] Error recording speedscope profile: {e}")
            return None

# Global instance
pyspy_manager = PySpyManager()
```

### 4. Unified Performance Manager

#### A. Main Performance Manager Class

```python
import json
import threading
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime

@dataclass
class PerformanceAlert:
    timestamp: float
    alert_type: str
    message: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    metrics: Dict[str, Any]

class PerformanceManager:
    """Unified performance monitoring manager"""

    def __init__(self):
        # Component instances
        self.psutil_monitor = psutil_monitor
        self.tracemalloc_monitor = tracemalloc_monitor
        self.pyspy_manager = pyspy_manager

        # Configuration
        self.config = {
            'enabled': False,
            'sample_interval': 1.0,
            'alert_thresholds': {
                'cpu_percent': 80.0,
                'memory_percent': 85.0,
                'memory_growth_mb_per_min': 5.0,
                'thread_count': 100
            },
            'auto_profiling': {
                'enabled': False,
                'cpu_threshold': 90.0,
                'duration': 30
            }
        }

        # State
        self.alerts = []
        self.alert_callbacks = []
        self._lock = threading.Lock()

        # Alert checking
        self.alert_check_interval = 5.0
        self.alert_thread = None
        self.stop_alert_checking = threading.Event()

    def configure(self, **kwargs):
        """Cấu hình performance manager"""
        for key, value in kwargs.items():
            if key in self.config:
                if isinstance(self.config[key], dict) and isinstance(value, dict):
                    self.config[key].update(value)
                else:
                    self.config[key] = value

    def start_monitoring(self):
        """Bắt đầu tất cả monitoring"""
        if not self.config['enabled']:
            print("[PerformanceManager] Monitoring disabled in config")
            return

        print("[PerformanceManager] Starting performance monitoring...")

        # Start core monitoring
        self.psutil_monitor.sample_interval = self.config['sample_interval']
        self.psutil_monitor.start_monitoring()

        # Start memory tracing
        self.tracemalloc_monitor.start_tracing()

        # Start alert checking
        self._start_alert_checking()

        print("[PerformanceManager] Performance monitoring started")

    def stop_monitoring(self):
        """Dừng tất cả monitoring"""
        print("[PerformanceManager] Stopping performance monitoring...")

        # Stop alert checking
        self._stop_alert_checking()

        # Stop core monitoring
        self.psutil_monitor.stop_monitoring()

        # Stop memory tracing
        self.tracemalloc_monitor.stop_tracing()

        print("[PerformanceManager] Performance monitoring stopped")

    def _start_alert_checking(self):
        """Bắt đầu alert checking thread"""
        if self.alert_thread is not None:
            return

        self.stop_alert_checking.clear()
        self.alert_thread = threading.Thread(
            target=self._alert_checking_loop,
            daemon=True,
            name="PerformanceAlertChecker"
        )
        self.alert_thread.start()

    def _stop_alert_checking(self):
        """Dừng alert checking thread"""
        if self.alert_thread is None:
            return

        self.stop_alert_checking.set()
        self.alert_thread.join(timeout=2.0)
        self.alert_thread = None

    def _alert_checking_loop(self):
        """Alert checking loop"""
        while not self.stop_alert_checking.wait(self.alert_check_interval):
            try:
                self._check_alerts()
            except Exception as e:
                print(f"[PerformanceManager] Error in alert checking: {e}")

    def _check_alerts(self):
        """Kiểm tra và trigger alerts"""
        current_metrics = self.get_current_metrics()

        if not current_metrics:
            return

        thresholds = self.config['alert_thresholds']
        alerts_triggered = []

        # Check system metrics
        if 'system' in current_metrics:
            system = current_metrics['system']

            # CPU alert
            if system.cpu_percent > thresholds['cpu_percent']:
                alert = PerformanceAlert(
                    timestamp=time.time(),
                    alert_type='high_cpu_usage',
                    message=f"High CPU usage: {system.cpu_percent:.1f}%",
                    severity='high' if system.cpu_percent > 95 else 'medium',
                    metrics={'cpu_percent': system.cpu_percent}
                )
                alerts_triggered.append(alert)

            # Memory alert
            if system.memory_percent > thresholds['memory_percent']:
                alert = PerformanceAlert(
                    timestamp=time.time(),
                    alert_type='high_memory_usage',
                    message=f"High memory usage: {system.memory_percent:.1f}%",
                    severity='high' if system.memory_percent > 95 else 'medium',
                    metrics={'memory_percent': system.memory_percent}
                )
                alerts_triggered.append(alert)

        # Check process metrics
        if 'process' in current_metrics:
            process = current_metrics['process']

            # Thread count alert
            if process.num_threads > thresholds['thread_count']:
                alert = PerformanceAlert(
                    timestamp=time.time(),
                    alert_type='high_thread_count',
                    message=f"High thread count: {process.num_threads}",
                    severity='medium',
                    metrics={'thread_count': process.num_threads}
                )
                alerts_triggered.append(alert)

        # Check memory growth
        memory_growth = self.tracemalloc_monitor.get_memory_growth()
        if memory_growth and memory_growth > thresholds['memory_growth_mb_per_min']:
            alert = PerformanceAlert(
                timestamp=time.time(),
                alert_type='memory_leak_detected',
                message=f"Potential memory leak: {memory_growth:.2f} MB/min growth",
                severity='high',
                metrics={'memory_growth_mb_per_min': memory_growth}
            )
            alerts_triggered.append(alert)

        # Process alerts
        for alert in alerts_triggered:
            self._process_alert(alert)

    def _process_alert(self, alert: PerformanceAlert):
        """Xử lý alert"""
        with self._lock:
            self.alerts.append(alert)

            # Keep only last 100 alerts
            if len(self.alerts) > 100:
                self.alerts = self.alerts[-100:]

        # Call alert callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                print(f"[PerformanceManager] Error in alert callback: {e}")

        # Auto profiling on high CPU
        auto_prof = self.config['auto_profiling']
        if (auto_prof['enabled'] and
            alert.alert_type == 'high_cpu_usage' and
            alert.severity == 'high'):

            self._trigger_auto_profiling(auto_prof['duration'])

    def _trigger_auto_profiling(self, duration: int):
        """Trigger automatic profiling"""
        print(f"[PerformanceManager] Triggering auto profiling for {duration}s")

        # Record flame graph in background
        def record_profile():
            output_path = self.pyspy_manager.record_flame_graph(duration)
            if output_path:
                print(f"[PerformanceManager] Auto profile saved: {output_path}")

        threading.Thread(target=record_profile, daemon=True).start()

    def add_alert_callback(self, callback: Callable[[PerformanceAlert], None]):
        """Thêm alert callback"""
        self.alert_callbacks.append(callback)

    def get_current_metrics(self) -> Dict[str, Any]:
        """Lấy tất cả metrics hiện tại"""
        metrics = {}

        # PSUtil metrics
        psutil_metrics = self.psutil_monitor.get_current_metrics()
        metrics.update(psutil_metrics)

        # Memory metrics
        memory_info = self.tracemalloc_monitor.get_current_memory_info()
        if memory_info:
            metrics['memory_trace'] = memory_info

        return metrics

    def get_performance_summary(self, duration_minutes: int = 10) -> Dict[str, Any]:
        """Lấy tổng kết performance"""
        summary = {
            'timestamp': time.time(),
            'duration_minutes': duration_minutes
        }

        # Thread performance
        thread_summary = self.psutil_monitor.get_thread_performance_summary(duration_minutes)
        if thread_summary:
            summary['thread_performance'] = thread_summary

        # Memory growth
        memory_growth = self.tracemalloc_monitor.get_memory_growth(duration_minutes)
        if memory_growth is not None:
            summary['memory_growth_mb_per_min'] = memory_growth

        # Recent alerts
        cutoff_time = time.time() - (duration_minutes * 60)
        recent_alerts = [
            asdict(alert) for alert in self.alerts
            if alert.timestamp >= cutoff_time
        ]
        summary['recent_alerts'] = recent_alerts

        return summary

    def export_performance_report(self, filepath: str, duration_minutes: int = 60):
        """Export comprehensive performance report"""
        report = {
            'export_info': {
                'timestamp': time.time(),
                'duration_minutes': duration_minutes,
                'iVMS_version': 'x.x.x',  # Get from your app
                'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
            },
            'configuration': self.config,
            'performance_summary': self.get_performance_summary(duration_minutes),
            'current_metrics': self.get_current_metrics()
        }

        # Export PSUtil data
        psutil_export_path = filepath.replace('.json', '_psutil.json')
        self.psutil_monitor.export_metrics(psutil_export_path, duration_minutes)
        report['psutil_data_file'] = psutil_export_path

        # Memory snapshots
        cutoff_time = time.time() - (duration_minutes * 60)
        memory_snapshots = [
            asdict(snapshot) for snapshot in self.tracemalloc_monitor.snapshots
            if snapshot.timestamp >= cutoff_time
        ]
        report['memory_snapshots'] = memory_snapshots

        with open(filepath, 'w') as f:
            json.dump(report, f, indent=2)

        print(f"[PerformanceManager] Performance report exported to {filepath}")

    def trigger_profiling(self, duration: int = 60, profile_type: str = 'flame') -> Optional[str]:
        """Trigger manual profiling"""
        if profile_type == 'flame':
            return self.pyspy_manager.record_flame_graph(duration)
        elif profile_type == 'speedscope':
            return self.pyspy_manager.record_speedscope_profile(duration)
        else:
            print(f"[PerformanceManager] Unknown profile type: {profile_type}")
            return None

    def get_call_stacks(self, include_locals: bool = False) -> Optional[str]:
        """Lấy current call stacks"""
        return self.pyspy_manager.dump_call_stacks(include_locals)

# Global instance
performance_manager = PerformanceManager()
```

## Integration với iVMS

### 1. Tích hợp vào Main Application

#### A. Trong VMS.py

```python
# VMS.py
import sys
from PySide6.QtWidgets import QApplication
from src.performance.performance_manager import performance_manager

def main():
    app = QApplication(sys.argv)

    # Configure performance monitoring
    performance_manager.configure(
        enabled=True,
        sample_interval=1.0,
        alert_thresholds={
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'memory_growth_mb_per_min': 5.0,
            'thread_count': 100
        },
        auto_profiling={
            'enabled': True,
            'cpu_threshold': 90.0,
            'duration': 30
        }
    )

    # Add alert callback
    def on_performance_alert(alert):
        print(f"[PERFORMANCE ALERT] {alert.severity.upper()}: {alert.message}")
        # Có thể gửi notification đến UI hoặc log file

    performance_manager.add_alert_callback(on_performance_alert)

    # Start monitoring
    performance_manager.start_monitoring()

    try:
        # Initialize main window
        main_window = MainWindow()
        main_window.show()

        # Run application
        exit_code = app.exec()

    finally:
        # Stop monitoring before exit
        performance_manager.stop_monitoring()

    return exit_code

if __name__ == "__main__":
    sys.exit(main())
```

#### B. Context Manager cho Operations

```python
# src/performance/operation_profiler.py
import time
import functools
from contextlib import contextmanager
from typing import Optional, Dict, Any
from src.performance.performance_manager import performance_manager

class OperationProfiler:
    """Context manager và decorator cho profiling operations"""

    def __init__(self):
        self.operation_stats = {}

    @contextmanager
    def profile_operation(self, operation_name: str,
                         take_memory_snapshot: bool = False):
        """Context manager để profile operation"""
        start_time = time.perf_counter()
        start_memory = None

        if take_memory_snapshot:
            start_memory = performance_manager.tracemalloc_monitor.take_snapshot()

        try:
            yield
        finally:
            end_time = time.perf_counter()
            duration = end_time - start_time

            # Record operation stats
            if operation_name not in self.operation_stats:
                self.operation_stats[operation_name] = {
                    'count': 0,
                    'total_time': 0,
                    'min_time': float('inf'),
                    'max_time': 0,
                    'avg_time': 0
                }

            stats = self.operation_stats[operation_name]
            stats['count'] += 1
            stats['total_time'] += duration
            stats['min_time'] = min(stats['min_time'], duration)
            stats['max_time'] = max(stats['max_time'], duration)
            stats['avg_time'] = stats['total_time'] / stats['count']

            # Memory snapshot comparison
            if take_memory_snapshot and start_memory:
                end_memory = performance_manager.tracemalloc_monitor.take_snapshot()
                if end_memory:
                    memory_diff = end_memory.current_mb - start_memory.current_mb
                    print(f"[OperationProfiler] {operation_name}: {duration:.4f}s, "
                          f"memory delta: {memory_diff:.2f}MB")

    def profile_function(self, operation_name: Optional[str] = None,
                        take_memory_snapshot: bool = False):
        """Decorator để profile function"""
        def decorator(func):
            nonlocal operation_name
            if operation_name is None:
                operation_name = f"{func.__module__}.{func.__name__}"

            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                with self.profile_operation(operation_name, take_memory_snapshot):
                    return func(*args, **kwargs)
                return wrapper
            return wrapper
        return decorator

    def get_operation_stats(self) -> Dict[str, Any]:
        """Lấy statistics của tất cả operations"""
        return self.operation_stats.copy()

    def reset_stats(self):
        """Reset tất cả statistics"""
        self.operation_stats.clear()

# Global instance
operation_profiler = OperationProfiler()
```

### 2. Tích hợp vào Video Processing

#### A. Trong VideoPlayerManager

```python
# src/common/camera/video_player_manager.py (modified)
from src.performance.operation_profiler import operation_profiler

class VideoPlayerManager(QObject):
    def process_player(self, widget, camera_model, stream_type):
        with operation_profiler.profile_operation("video_player_processing"):
            player = self.get_player(camera_model=camera_model, stream_type=stream_type)
            # ... existing code ...

    @operation_profiler.profile_function("video_frame_processing", take_memory_snapshot=True)
    def process_frame(self, frame_data):
        # Existing frame processing code
        pass
```

#### B. Trong FrameModel

```python
# src/common/qml/models/frame_model.py (modified)
from src.performance.operation_profiler import operation_profiler

class FrameModel(QObject):
    @operation_profiler.profile_function("frame_model_paint")
    def paint(self, painter, option, widget):
        # Existing paint code
        pass

    def get_frame(self):
        with operation_profiler.profile_operation("frame_acquisition"):
            # Existing get_frame code
            pass
```

### 3. Performance Dashboard Integration

#### A. Performance Dashboard QML

```python
# src/performance/performance_dashboard.py
from PySide6.QtCore import QObject, Signal, Slot, QTimer, Property
from src.performance.performance_manager import performance_manager
import json

class PerformanceDashboard(QObject):
    """Performance dashboard cho QML UI"""

    # Signals
    metricsUpdated = Signal(str)  # JSON string
    alertTriggered = Signal(str, str, str)  # type, message, severity

    def __init__(self, parent=None):
        super().__init__(parent)

        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_metrics)
        self.update_timer.setInterval(2000)  # 2 seconds

        # Connect to performance manager alerts
        performance_manager.add_alert_callback(self._on_alert)

    @Slot()
    def start_dashboard(self):
        """Bắt đầu dashboard updates"""
        self.update_timer.start()

    @Slot()
    def stop_dashboard(self):
        """Dừng dashboard updates"""
        self.update_timer.stop()

    @Slot()
    def update_metrics(self):
        """Update metrics và emit signal"""
        try:
            metrics = performance_manager.get_current_metrics()
            metrics_json = json.dumps(metrics, default=str)
            self.metricsUpdated.emit(metrics_json)
        except Exception as e:
            print(f"[PerformanceDashboard] Error updating metrics: {e}")

    def _on_alert(self, alert):
        """Handle performance alert"""
        self.alertTriggered.emit(alert.alert_type, alert.message, alert.severity)

    @Slot(int, result=str)
    def get_performance_summary(self, duration_minutes: int = 10) -> str:
        """Lấy performance summary"""
        try:
            summary = performance_manager.get_performance_summary(duration_minutes)
            return json.dumps(summary, default=str)
        except Exception as e:
            print(f"[PerformanceDashboard] Error getting summary: {e}")
            return "{}"

    @Slot(int, str, result=str)
    def trigger_profiling(self, duration: int, profile_type: str) -> str:
        """Trigger profiling từ UI"""
        try:
            output_path = performance_manager.trigger_profiling(duration, profile_type)
            return output_path or ""
        except Exception as e:
            print(f"[PerformanceDashboard] Error triggering profiling: {e}")
            return ""

    @Slot(result=str)
    def get_call_stacks(self) -> str:
        """Lấy current call stacks"""
        try:
            stacks = performance_manager.get_call_stacks()
            return stacks or ""
        except Exception as e:
            print(f"[PerformanceDashboard] Error getting call stacks: {e}")
            return ""

    @Slot(str)
    def export_report(self, filepath: str):
        """Export performance report"""
        try:
            performance_manager.export_performance_report(filepath)
        except Exception as e:
            print(f"[PerformanceDashboard] Error exporting report: {e}")

# Register for QML
from PySide6.QtQml import qmlRegisterType
qmlRegisterType(PerformanceDashboard, "PerformanceMonitor", 1, 0, "PerformanceDashboard")
```

### 4. Configuration Management

#### A. Performance Configuration

```python
# src/performance/performance_config.py
import json
from pathlib import Path
from typing import Dict, Any

class PerformanceConfig:
    """Configuration management cho performance monitoring"""

    DEFAULT_CONFIG = {
        'monitoring': {
            'enabled': True,
            'sample_interval': 1.0,
            'max_samples': 3600,
            'memory_tracing': True
        },
        'alerts': {
            'enabled': True,
            'thresholds': {
                'cpu_percent': 80.0,
                'memory_percent': 85.0,
                'memory_growth_mb_per_min': 5.0,
                'thread_count': 100
            },
            'check_interval': 5.0
        },
        'auto_profiling': {
            'enabled': False,
            'cpu_threshold': 90.0,
            'duration': 30,
            'max_profiles_per_hour': 3
        },
        'export': {
            'auto_export': False,
            'export_interval_hours': 24,
            'keep_days': 7,
            'export_directory': 'performance_data'
        }
    }

    def __init__(self, config_file: str = "performance_config.json"):
        self.config_file = Path(config_file)
        self.config = self.DEFAULT_CONFIG.copy()
        self.load_config()

    def load_config(self):
        """Load configuration từ file"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    loaded_config = json.load(f)
                    self._merge_config(self.config, loaded_config)
                print(f"[PerformanceConfig] Loaded config from {self.config_file}")
            except Exception as e:
                print(f"[PerformanceConfig] Error loading config: {e}")

    def save_config(self):
        """Save configuration ra file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            print(f"[PerformanceConfig] Saved config to {self.config_file}")
        except Exception as e:
            print(f"[PerformanceConfig] Error saving config: {e}")

    def _merge_config(self, base: Dict[str, Any], update: Dict[str, Any]):
        """Merge configuration dictionaries"""
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value

    def get(self, key_path: str, default=None):
        """Lấy config value bằng dot notation"""
        keys = key_path.split('.')
        value = self.config

        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default

        return value

    def set(self, key_path: str, value):
        """Set config value bằng dot notation"""
        keys = key_path.split('.')
        config = self.config

        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]

        config[keys[-1]] = value

    def get_monitoring_config(self) -> Dict[str, Any]:
        """Lấy monitoring configuration"""
        return self.config.get('monitoring', {})

    def get_alert_config(self) -> Dict[str, Any]:
        """Lấy alert configuration"""
        return self.config.get('alerts', {})

    def get_profiling_config(self) -> Dict[str, Any]:
        """Lấy auto profiling configuration"""
        return self.config.get('auto_profiling', {})

# Global instance
performance_config = PerformanceConfig()
```

## Usage Examples

### 1. Basic Setup và Monitoring

#### A. Khởi tạo cơ bản

```python
# main.py
from src.performance.performance_manager import performance_manager
from src.performance.performance_config import performance_config

def setup_performance_monitoring():
    """Setup performance monitoring cho iVMS"""

    # Load configuration
    config = performance_config.get_monitoring_config()
    alert_config = performance_config.get_alert_config()

    # Configure performance manager
    performance_manager.configure(
        enabled=config.get('enabled', True),
        sample_interval=config.get('sample_interval', 1.0),
        alert_thresholds=alert_config.get('thresholds', {}),
        auto_profiling=performance_config.get_profiling_config()
    )

    # Add custom alert handler
    def handle_performance_alert(alert):
        if alert.severity == 'critical':
            # Log critical alerts
            print(f"CRITICAL PERFORMANCE ALERT: {alert.message}")
            # Có thể gửi email hoặc notification

        elif alert.severity == 'high':
            # Log high severity alerts
            print(f"HIGH PERFORMANCE ALERT: {alert.message}")

    performance_manager.add_alert_callback(handle_performance_alert)

    # Start monitoring
    performance_manager.start_monitoring()
    print("Performance monitoring started")

# Trong main application
if __name__ == "__main__":
    setup_performance_monitoring()

    try:
        # Run your application
        run_ivms_application()
    finally:
        performance_manager.stop_monitoring()
```

#### B. Profiling specific operations

```python
# Trong video processing code
from src.performance.operation_profiler import operation_profiler

class CameraProcessor:
    def __init__(self):
        self.frame_count = 0

    @operation_profiler.profile_function("camera_frame_processing")
    def process_camera_frame(self, frame_data):
        """Process camera frame với automatic profiling"""
        # Your frame processing logic
        processed_frame = self.apply_filters(frame_data)
        return processed_frame

    def process_video_stream(self, stream_data):
        """Process video stream với manual profiling"""
        with operation_profiler.profile_operation("video_stream_processing",
                                                 take_memory_snapshot=True):
            # Your video processing logic
            for frame in stream_data:
                self.process_camera_frame(frame)
                self.frame_count += 1

    def get_processing_stats(self):
        """Lấy processing statistics"""
        stats = operation_profiler.get_operation_stats()
        return {
            'frame_processing': stats.get('camera_frame_processing', {}),
            'stream_processing': stats.get('video_stream_processing', {}),
            'total_frames': self.frame_count
        }
```

### 2. Real-time Monitoring Dashboard

#### A. QML Performance Dashboard

```qml
// PerformanceDashboard.qml
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtCharts 2.15
import PerformanceMonitor 1.0

Item {
    id: root

    property bool monitoringActive: false

    PerformanceDashboard {
        id: perfDashboard

        onMetricsUpdated: function(metricsJson) {
            var metrics = JSON.parse(metricsJson)
            updateCharts(metrics)
        }

        onAlertTriggered: function(alertType, message, severity) {
            showAlert(alertType, message, severity)
        }
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: 10

        // Control Panel
        RowLayout {
            Layout.fillWidth: true

            Button {
                text: monitoringActive ? "Stop Monitoring" : "Start Monitoring"
                onClicked: {
                    if (monitoringActive) {
                        perfDashboard.stop_dashboard()
                    } else {
                        perfDashboard.start_dashboard()
                    }
                    monitoringActive = !monitoringActive
                }
            }

            Button {
                text: "Generate Flame Graph"
                onClicked: {
                    var outputPath = perfDashboard.trigger_profiling(60, "flame")
                    if (outputPath) {
                        console.log("Flame graph saved to:", outputPath)
                    }
                }
            }

            Button {
                text: "Export Report"
                onClicked: {
                    perfDashboard.export_report("performance_report.json")
                }
            }
        }

        // Metrics Display
        GridLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true
            columns: 2

            // CPU Chart
            ChartView {
                id: cpuChart
                Layout.fillWidth: true
                Layout.fillHeight: true
                title: "CPU Usage (%)"

                LineSeries {
                    id: cpuSeries
                    name: "CPU %"
                }
            }

            // Memory Chart
            ChartView {
                id: memoryChart
                Layout.fillWidth: true
                Layout.fillHeight: true
                title: "Memory Usage (MB)"

                LineSeries {
                    id: memorySeries
                    name: "Memory MB"
                }
            }

            // Thread Count
            ChartView {
                id: threadChart
                Layout.fillWidth: true
                Layout.fillHeight: true
                title: "Thread Count"

                LineSeries {
                    id: threadSeries
                    name: "Threads"
                }
            }

            // Alerts Panel
            Rectangle {
                Layout.fillWidth: true
                Layout.fillHeight: true
                color: "#f0f0f0"
                border.color: "#ccc"

                ScrollView {
                    anchors.fill: parent
                    anchors.margins: 5

                    ListView {
                        id: alertsList
                        model: ListModel {
                            id: alertsModel
                        }

                        delegate: Rectangle {
                            width: alertsList.width
                            height: 40
                            color: {
                                switch(model.severity) {
                                    case "critical": return "#ffebee"
                                    case "high": return "#fff3e0"
                                    case "medium": return "#f3e5f5"
                                    default: return "#e8f5e8"
                                }
                            }

                            Text {
                                anchors.left: parent.left
                                anchors.verticalCenter: parent.verticalCenter
                                anchors.margins: 5
                                text: model.message
                                font.pointSize: 10
                            }

                            Text {
                                anchors.right: parent.right
                                anchors.verticalCenter: parent.verticalCenter
                                anchors.margins: 5
                                text: model.time
                                font.pointSize: 8
                                color: "#666"
                            }
                        }
                    }
                }
            }
        }
    }

    function updateCharts(metrics) {
        var timestamp = Date.now()

        if (metrics.system) {
            cpuSeries.append(timestamp, metrics.system.cpu_percent)
            memorySeries.append(timestamp, metrics.system.memory_used_mb)
        }

        if (metrics.process) {
            threadSeries.append(timestamp, metrics.process.num_threads)
        }

        // Keep only last 100 points
        if (cpuSeries.count > 100) {
            cpuSeries.removePoints(0, cpuSeries.count - 100)
        }
        if (memorySeries.count > 100) {
            memorySeries.removePoints(0, memorySeries.count - 100)
        }
        if (threadSeries.count > 100) {
            threadSeries.removePoints(0, threadSeries.count - 100)
        }
    }

    function showAlert(alertType, message, severity) {
        alertsModel.insert(0, {
            "type": alertType,
            "message": message,
            "severity": severity,
            "time": new Date().toLocaleTimeString()
        })

        // Keep only last 50 alerts
        if (alertsModel.count > 50) {
            alertsModel.remove(50, alertsModel.count - 50)
        }
    }
}
```

### 3. Command Line Tools

#### A. Performance CLI Tool

```python
#!/usr/bin/env python3
# tools/performance_cli.py
import argparse
import sys
import time
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from performance.performance_manager import performance_manager
from performance.pyspy_manager import pyspy_manager

def cmd_monitor(args):
    """Real-time monitoring command"""
    print(f"Starting performance monitoring for {args.duration} seconds...")

    performance_manager.configure(enabled=True, sample_interval=args.interval)
    performance_manager.start_monitoring()

    try:
        start_time = time.time()
        while time.time() - start_time < args.duration:
            metrics = performance_manager.get_current_metrics()

            if metrics:
                if 'system' in metrics:
                    sys_metrics = metrics['system']
                    print(f"CPU: {sys_metrics.cpu_percent:5.1f}% | "
                          f"Memory: {sys_metrics.memory_percent:5.1f}% | "
                          f"Memory Used: {sys_metrics.memory_used_mb:7.1f}MB")

                if 'process' in metrics:
                    proc_metrics = metrics['process']
                    print(f"Process CPU: {proc_metrics.cpu_percent:5.1f}% | "
                          f"Process Memory: {proc_metrics.memory_rss_mb:7.1f}MB | "
                          f"Threads: {proc_metrics.num_threads}")

            time.sleep(args.interval)

    except KeyboardInterrupt:
        print("\nMonitoring stopped by user")
    finally:
        performance_manager.stop_monitoring()

def cmd_profile(args):
    """Profiling command"""
    if not pyspy_manager.is_available():
        print("Error: py-spy is not available. Please install it with: pip install py-spy")
        return 1

    print(f"Starting {args.type} profiling for {args.duration} seconds...")

    if args.type == 'flame':
        output_path = pyspy_manager.record_flame_graph(args.duration, args.output)
    elif args.type == 'speedscope':
        output_path = pyspy_manager.record_speedscope_profile(args.duration, args.output)
    else:
        print(f"Error: Unknown profile type '{args.type}'")
        return 1

    if output_path:
        print(f"Profile saved to: {output_path}")
        return 0
    else:
        print("Error: Failed to generate profile")
        return 1

def cmd_dump(args):
    """Dump call stacks command"""
    if not pyspy_manager.is_available():
        print("Error: py-spy is not available")
        return 1

    print("Dumping current call stacks...")
    stacks = pyspy_manager.dump_call_stacks(args.locals)

    if stacks:
        if args.output:
            with open(args.output, 'w') as f:
                f.write(stacks)
            print(f"Call stacks saved to: {args.output}")
        else:
            print(stacks)
        return 0
    else:
        print("Error: Failed to dump call stacks")
        return 1

def cmd_report(args):
    """Generate performance report"""
    print(f"Generating performance report for {args.duration} minutes...")

    performance_manager.configure(enabled=True)
    performance_manager.start_monitoring()

    try:
        # Wait for data collection
        time.sleep(min(args.duration * 60, 60))  # Max 1 minute wait

        # Generate report
        performance_manager.export_performance_report(args.output, args.duration)
        print(f"Performance report saved to: {args.output}")

    finally:
        performance_manager.stop_monitoring()

def main():
    parser = argparse.ArgumentParser(description="iVMS Performance Monitoring CLI")
    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # Monitor command
    monitor_parser = subparsers.add_parser('monitor', help='Real-time monitoring')
    monitor_parser.add_argument('-d', '--duration', type=int, default=60,
                               help='Monitoring duration in seconds (default: 60)')
    monitor_parser.add_argument('-i', '--interval', type=float, default=1.0,
                               help='Sampling interval in seconds (default: 1.0)')

    # Profile command
    profile_parser = subparsers.add_parser('profile', help='Generate performance profile')
    profile_parser.add_argument('type', choices=['flame', 'speedscope'],
                               help='Profile type')
    profile_parser.add_argument('-d', '--duration', type=int, default=60,
                               help='Profiling duration in seconds (default: 60)')
    profile_parser.add_argument('-o', '--output', type=str,
                               help='Output file path')

    # Dump command
    dump_parser = subparsers.add_parser('dump', help='Dump current call stacks')
    dump_parser.add_argument('-l', '--locals', action='store_true',
                            help='Include local variables')
    dump_parser.add_argument('-o', '--output', type=str,
                            help='Output file path')

    # Report command
    report_parser = subparsers.add_parser('report', help='Generate performance report')
    report_parser.add_argument('-d', '--duration', type=int, default=10,
                              help='Report duration in minutes (default: 10)')
    report_parser.add_argument('-o', '--output', type=str, default='performance_report.json',
                              help='Output file path (default: performance_report.json)')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return 1

    # Execute command
    if args.command == 'monitor':
        return cmd_monitor(args)
    elif args.command == 'profile':
        return cmd_profile(args)
    elif args.command == 'dump':
        return cmd_dump(args)
    elif args.command == 'report':
        return cmd_report(args)
    else:
        print(f"Unknown command: {args.command}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
```

## Best Practices

### 1. Performance Monitoring Guidelines

#### A. Monitoring Strategy
- **Continuous Monitoring**: Chạy monitoring liên tục với sample rate thấp (1-2 giây)
- **Alert Thresholds**: Đặt threshold phù hợp với hardware và workload
- **Memory Tracking**: Bật tracemalloc chỉ khi cần thiết để tránh overhead
- **Auto Profiling**: Sử dụng auto profiling cho high CPU situations

#### B. Resource Management
- **Thread Safety**: Tất cả components đều thread-safe
- **Memory Usage**: Giới hạn số lượng samples để tránh memory leak
- **File I/O**: Export data định kỳ để tránh mất dữ liệu
- **Cleanup**: Luôn stop monitoring khi thoát ứng dụng

### 2. Troubleshooting Common Issues

#### A. High CPU Usage
```python
# Khi CPU usage cao, tự động trigger profiling
def handle_high_cpu_alert(alert):
    if alert.alert_type == 'high_cpu_usage' and alert.severity == 'high':
        # Generate flame graph
        flame_path = performance_manager.trigger_profiling(30, 'flame')

        # Get thread summary
        thread_summary = performance_manager.get_thread_performance_summary(5)

        # Log top CPU consuming threads
        sorted_threads = sorted(thread_summary.items(),
                               key=lambda x: x[1]['avg_cpu_time'],
                               reverse=True)

        print("Top CPU consuming threads:")
        for thread_name, stats in sorted_threads[:5]:
            print(f"  {thread_name}: {stats['avg_cpu_time']:.4f}s avg")
```

#### B. Memory Leaks
```python
# Định kỳ check memory leaks
def check_memory_leaks():
    if performance_manager.tracemalloc_monitor.detect_memory_leaks():
        # Take detailed snapshot
        snapshot = performance_manager.tracemalloc_monitor.take_snapshot(20)

        # Log top allocations
        print("Potential memory leak detected!")
        print("Top memory allocations:")
        for location, size_mb in snapshot.top_allocations:
            print(f"  {location}: {size_mb:.2f}MB")
```

### 3. Integration Checklist

- [ ] Performance monitoring configured trong main application
- [ ] Alert callbacks setup cho critical issues
- [ ] Operation profiling added cho performance-critical functions
- [ ] Memory tracking enabled cho memory-intensive operations
- [ ] Dashboard integrated cho real-time monitoring
- [ ] CLI tools available cho debugging
- [ ] Export functionality setup cho analysis
- [ ] Configuration management implemented
- [ ] Cleanup procedures trong application shutdown
- [ ] Documentation updated với performance guidelines

## Kết luận

Hệ thống performance tracking này cung cấp:

1. **Comprehensive Monitoring** - System, process, và thread level metrics
2. **Non-intrusive Profiling** - Sử dụng py-spy cho external profiling
3. **Memory Leak Detection** - Tracemalloc và pympler integration
4. **Real-time Alerts** - Configurable thresholds và callbacks
5. **Visual Dashboard** - QML-based real-time monitoring
6. **Export Capabilities** - Detailed reports cho analysis
7. **CLI Tools** - Command line tools cho debugging
8. **Production Ready** - Safe cho production environment

Với cách tiếp cận này, bạn có thể:
- **Monitor continuously** mà không ảnh hưởng performance
- **Debug issues quickly** với flame graphs và call stacks
- **Detect problems early** với alert system
- **Analyze trends** với exported data
- **Optimize performance** dựa trên real data
```
```
```
