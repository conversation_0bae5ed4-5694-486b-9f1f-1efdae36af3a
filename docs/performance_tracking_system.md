# Hệ thống Tracking Hiệu năng Realtime cho iVMS

## Tổng quan

Tài liệu này mô tả cơ chế tracking hiệu năng realtime cho ứng dụng iVMS mà không ảnh hưởng đến hiệu năng hệ thống và có khả năng xác định threading nào đang sử dụng tài nguyên nặng.

## Kiến trúc Hệ thống

### 1. <PERSON><PERSON><PERSON><PERSON> tắc Thiết kế

- **Non-intrusive**: Không ảnh hưởng đến luồng chính của ứng dụng
- **Low overhead**: Sử dụng tối thiểu CPU và memory
- **Asynchronous**: Tấ<PERSON> cả tracking chạy trên thread riêng biệt
- **Configurable**: <PERSON><PERSON> thể bật/tắt và cấu hình chi tiết
- **Thread-safe**: An toàn khi sử dụng đa luồng

### 2. Th<PERSON><PERSON> phần <PERSON>h

```
PerformanceTracker (Singleton)
├── MetricsCollector (Thread riêng)
├── ThreadMonitor (<PERSON> từng thread)
├── ResourceMonitor (CPU, Memory, GPU)
├── EventLogger (Ghi log hiệu năng)
└── RealtimeReporter (Báo cáo realtime)
```

## Chi tiết Triển khai

### 1. Performance Tracker Core

```python
class PerformanceTracker(QObject):
    """
    Core tracking system - chạy trên thread riêng biệt
    Sử dụng sampling thay vì continuous monitoring
    """
    
    # Cấu hình sampling
    SAMPLE_INTERVAL = 1.0  # 1 giây
    THREAD_SAMPLE_INTERVAL = 0.5  # 0.5 giây cho thread monitoring
    
    # Metrics được thu thập
    - CPU usage per thread
    - Memory usage per thread  
    - GPU usage (nếu có)
    - Network I/O
    - Disk I/O
    - Qt Event queue length
    - Frame rate của video streams
```

### 2. Thread Monitoring Strategy

#### A. Lightweight Thread Tracking
```python
class ThreadMonitor:
    """
    Theo dõi hiệu năng từng thread mà không block
    """
    
    def track_thread_performance(self, thread_id):
        # Sử dụng psutil để lấy thông tin thread
        # Chỉ sample khi cần thiết
        # Không gọi trực tiếp từ thread được monitor
```

#### B. Critical Threads Identification
Dựa trên kiến trúc hiện tại, các thread quan trọng cần monitor:

1. **Main UI Thread** - Qt Event Loop
2. **Video Player Threads** - `VideoPlayerManager` với 64 max threads
3. **Worker Threads** - `WorkerThread` cho ONVIF operations
4. **Thread Pool** - `ThreadPoolManager` pools
5. **WebSocket Thread** - Real-time communication
6. **Camera Stream Threads** - Video decoding/rendering

### 3. Sampling Strategy

#### A. Adaptive Sampling
```python
class AdaptiveSampler:
    """
    Điều chỉnh tần suất sampling dựa trên tải hệ thống
    """
    
    # Tải thấp: Sample mỗi 2 giây
    # Tải trung bình: Sample mỗi 1 giây  
    # Tải cao: Sample mỗi 0.5 giây
    # Tải cực cao: Sample mỗi 0.2 giây
```

#### B. Smart Filtering
```python
class MetricsFilter:
    """
    Chỉ thu thập metrics khi có thay đổi đáng kể
    """
    
    # Chỉ log khi CPU usage thay đổi > 5%
    # Chỉ log khi Memory usage thay đổi > 10MB
    # Sử dụng moving average để làm mịn data
```

### 4. Data Collection Methods

#### A. System-level Metrics
```python
import psutil
import threading
import time

class SystemMetricsCollector:
    def collect_system_metrics(self):
        return {
            'cpu_percent': psutil.cpu_percent(interval=None),
            'memory_info': psutil.virtual_memory(),
            'disk_io': psutil.disk_io_counters(),
            'network_io': psutil.net_io_counters(),
            'timestamp': time.time()
        }
```

#### B. Thread-specific Metrics  
```python
class ThreadMetricsCollector:
    def collect_thread_metrics(self):
        current_process = psutil.Process()
        threads_info = []
        
        for thread in current_process.threads():
            thread_info = {
                'thread_id': thread.id,
                'user_time': thread.user_time,
                'system_time': thread.system_time,
                'name': self.get_thread_name(thread.id)
            }
            threads_info.append(thread_info)
            
        return threads_info
```

#### C. Application-specific Metrics
```python
class AppMetricsCollector:
    def collect_app_metrics(self):
        return {
            'active_cameras': len(camera_model_manager.get_all_cameras()),
            'thread_pool_active': threadPoolManager.get_active_count(),
            'qt_event_queue_size': QCoreApplication.instance().eventQueue.size(),
            'video_frame_rate': self.get_average_fps(),
            'memory_leaks': self.detect_memory_leaks()
        }
```

### 5. Real-time Reporting

#### A. Dashboard Integration
```python
class PerformanceDashboard(QObject):
    """
    Hiển thị metrics realtime trong UI
    """
    
    # Sử dụng Qt Charts để hiển thị graphs
    # Update mỗi 2 giây để không làm lag UI
    # Chỉ hiển thị khi user mở performance tab
```

#### B. Alert System
```python
class PerformanceAlerts:
    """
    Cảnh báo khi có vấn đề hiệu năng
    """
    
    THRESHOLDS = {
        'cpu_usage': 80,      # %
        'memory_usage': 85,   # %
        'thread_count': 100,  # số lượng
        'frame_drop_rate': 5  # %
    }
```

### 6. Storage & Analysis

#### A. Circular Buffer
```python
class MetricsBuffer:
    """
    Lưu trữ metrics trong memory với kích thước cố định
    """
    
    MAX_SAMPLES = 3600  # 1 giờ data với sample rate 1s
    
    # Tự động xóa data cũ khi buffer đầy
    # Hỗ trợ query theo time range
    # Export data ra file khi cần
```

#### B. Trend Analysis
```python
class TrendAnalyzer:
    """
    Phân tích xu hướng hiệu năng
    """
    
    def detect_performance_degradation(self):
        # Phát hiện xu hướng giảm hiệu năng
        # Dự đoán bottleneck sắp xảy ra
        # Đề xuất optimization
```

## Cấu hình và Sử dụng

### 1. Configuration File
```json
{
    "performance_tracking": {
        "enabled": true,
        "sample_interval": 1.0,
        "thread_monitoring": true,
        "metrics_to_collect": [
            "cpu_usage",
            "memory_usage", 
            "thread_performance",
            "video_metrics"
        ],
        "alert_thresholds": {
            "cpu_usage": 80,
            "memory_usage": 85
        },
        "storage": {
            "max_samples": 3600,
            "export_enabled": true,
            "export_path": "logs/performance/"
        }
    }
}
```

### 2. API Usage
```python
# Khởi tạo
tracker = PerformanceTracker.get_instance()
tracker.start_monitoring()

# Lấy metrics hiện tại
current_metrics = tracker.get_current_metrics()

# Lấy thread performance
thread_metrics = tracker.get_thread_metrics()

# Xuất báo cáo
tracker.export_report("performance_report.json")
```

### 3. Integration Points

#### A. Trong Main Application
```python
# VMS.py
def main():
    app = QApplication(sys.argv)
    
    # Khởi tạo performance tracking
    if Config.PERFORMANCE_TRACKING_ENABLED:
        performance_tracker.start_monitoring()
    
    # ... rest of app initialization
```

#### B. Trong Critical Components
```python
# video_player_manager.py
class VideoPlayerManager:
    def process_frame(self):
        with performance_tracker.measure_operation("video_processing"):
            # Video processing logic
            pass
```

## Lợi ích

### 1. Không ảnh hưởng hiệu năng
- Chạy trên thread riêng biệt
- Sử dụng sampling thay vì continuous monitoring
- Adaptive sampling rate dựa trên tải hệ thống

### 2. Xác định threading bottleneck
- Monitor từng thread riêng biệt
- Phát hiện thread nào consume CPU/memory nhiều nhất
- Tracking thread lifecycle và resource usage

### 3. Proactive monitoring
- Cảnh báo sớm khi có vấn đề
- Trend analysis để dự đoán bottleneck
- Automatic optimization suggestions

### 4. Developer-friendly
- Rich dashboard với charts và graphs
- Export data để phân tích offline
- Integration với existing logging system

## Implementation Examples

### 1. Core Performance Tracker Class

```python
import psutil
import threading
import time
import json
from collections import deque, defaultdict
from PySide6.QtCore import QObject, Signal, QTimer, QThread
from typing import Dict, List, Any, Optional

class PerformanceTracker(QObject):
    # Signals for real-time updates
    metrics_updated = Signal(dict)
    alert_triggered = Signal(str, dict)

    _instance = None

    def __init__(self):
        super().__init__()
        self.enabled = False
        self.sample_interval = 1.0
        self.metrics_buffer = deque(maxlen=3600)  # 1 hour of data
        self.thread_metrics = defaultdict(deque)
        self.alert_thresholds = {
            'cpu_usage': 80,
            'memory_usage': 85,
            'thread_count': 100
        }

        # Monitoring thread
        self.monitor_thread = None
        self.stop_monitoring = threading.Event()

        # Current process reference
        self.process = psutil.Process()

    @staticmethod
    def get_instance():
        if PerformanceTracker._instance is None:
            PerformanceTracker._instance = PerformanceTracker()
        return PerformanceTracker._instance

    def start_monitoring(self):
        """Bắt đầu monitoring trên thread riêng biệt"""
        if self.enabled and self.monitor_thread is None:
            self.stop_monitoring.clear()
            self.monitor_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True
            )
            self.monitor_thread.start()
            print("[PerformanceTracker] Monitoring started")

    def stop_monitoring(self):
        """Dừng monitoring"""
        if self.monitor_thread:
            self.stop_monitoring.set()
            self.monitor_thread.join(timeout=2.0)
            self.monitor_thread = None
            print("[PerformanceTracker] Monitoring stopped")

    def _monitoring_loop(self):
        """Main monitoring loop - chạy trên thread riêng"""
        while not self.stop_monitoring.wait(self.sample_interval):
            try:
                metrics = self._collect_metrics()
                self.metrics_buffer.append(metrics)

                # Check alerts
                self._check_alerts(metrics)

                # Emit signal for UI updates
                self.metrics_updated.emit(metrics)

            except Exception as e:
                print(f"[PerformanceTracker] Error in monitoring loop: {e}")

    def _collect_metrics(self) -> Dict[str, Any]:
        """Thu thập tất cả metrics"""
        timestamp = time.time()

        # System metrics
        cpu_percent = psutil.cpu_percent(interval=None)
        memory_info = psutil.virtual_memory()

        # Thread metrics
        thread_metrics = self._collect_thread_metrics()

        # Application specific metrics
        app_metrics = self._collect_app_metrics()

        return {
            'timestamp': timestamp,
            'system': {
                'cpu_percent': cpu_percent,
                'memory_percent': memory_info.percent,
                'memory_used_mb': memory_info.used / (1024 * 1024),
                'memory_available_mb': memory_info.available / (1024 * 1024)
            },
            'threads': thread_metrics,
            'application': app_metrics
        }

    def _collect_thread_metrics(self) -> List[Dict[str, Any]]:
        """Thu thập metrics của từng thread"""
        try:
            threads_info = []
            for thread in self.process.threads():
                thread_info = {
                    'id': thread.id,
                    'user_time': thread.user_time,
                    'system_time': thread.system_time,
                    'name': self._get_thread_name(thread.id)
                }
                threads_info.append(thread_info)
            return threads_info
        except Exception as e:
            print(f"[PerformanceTracker] Error collecting thread metrics: {e}")
            return []

    def _get_thread_name(self, thread_id: int) -> str:
        """Lấy tên thread từ ID"""
        # Map thread ID to meaningful names based on your application
        thread_names = {
            # Main thread sẽ có ID cụ thể
            # Video player threads
            # Worker threads
            # etc.
        }
        return thread_names.get(thread_id, f"Thread-{thread_id}")

    def _collect_app_metrics(self) -> Dict[str, Any]:
        """Thu thập metrics specific cho ứng dụng"""
        try:
            from src.common.model.camera_model import camera_model_manager
            from src.common.threads.thread_pool_manager import threadPoolManager

            return {
                'active_cameras': len(camera_model_manager.get_all_cameras()) if camera_model_manager else 0,
                'thread_pools': self._get_thread_pool_metrics(),
                'qt_objects': self._count_qt_objects(),
                'video_streams': self._get_video_stream_metrics()
            }
        except Exception as e:
            print(f"[PerformanceTracker] Error collecting app metrics: {e}")
            return {}

    def _get_thread_pool_metrics(self) -> Dict[str, int]:
        """Lấy metrics của thread pools"""
        try:
            from src.common.threads.thread_pool_manager import threadPoolManager

            pool_metrics = {}
            for pool_name, pool in threadPoolManager._pools.items():
                pool_metrics[pool_name] = {
                    'active_threads': pool.activeThreadCount(),
                    'max_threads': pool.maxThreadCount()
                }
            return pool_metrics
        except:
            return {}

    def _count_qt_objects(self) -> int:
        """Đếm số lượng Qt objects (để phát hiện memory leaks)"""
        try:
            from PySide6.QtCore import QCoreApplication
            app = QCoreApplication.instance()
            return len(app.allWidgets()) if app else 0
        except:
            return 0

    def _get_video_stream_metrics(self) -> Dict[str, Any]:
        """Lấy metrics của video streams"""
        # Implementation depends on your video player architecture
        return {
            'active_streams': 0,
            'total_frames_processed': 0,
            'average_fps': 0.0,
            'dropped_frames': 0
        }

    def _check_alerts(self, metrics: Dict[str, Any]):
        """Kiểm tra và trigger alerts"""
        system_metrics = metrics.get('system', {})

        # CPU alert
        cpu_usage = system_metrics.get('cpu_percent', 0)
        if cpu_usage > self.alert_thresholds['cpu_usage']:
            self.alert_triggered.emit('high_cpu_usage', {
                'current': cpu_usage,
                'threshold': self.alert_thresholds['cpu_usage']
            })

        # Memory alert
        memory_usage = system_metrics.get('memory_percent', 0)
        if memory_usage > self.alert_thresholds['memory_usage']:
            self.alert_triggered.emit('high_memory_usage', {
                'current': memory_usage,
                'threshold': self.alert_thresholds['memory_usage']
            })

    def get_current_metrics(self) -> Optional[Dict[str, Any]]:
        """Lấy metrics hiện tại"""
        return self.metrics_buffer[-1] if self.metrics_buffer else None

    def get_thread_performance_summary(self) -> Dict[str, Any]:
        """Lấy tổng kết hiệu năng của các threads"""
        if not self.metrics_buffer:
            return {}

        # Analyze last 10 samples
        recent_samples = list(self.metrics_buffer)[-10:]
        thread_summary = defaultdict(lambda: {'cpu_time': 0, 'samples': 0})

        for sample in recent_samples:
            for thread in sample.get('threads', []):
                thread_id = thread['id']
                thread_name = thread['name']
                total_time = thread['user_time'] + thread['system_time']

                thread_summary[thread_name]['cpu_time'] += total_time
                thread_summary[thread_name]['samples'] += 1
                thread_summary[thread_name]['id'] = thread_id

        # Calculate averages
        for thread_name, data in thread_summary.items():
            if data['samples'] > 0:
                data['avg_cpu_time'] = data['cpu_time'] / data['samples']

        return dict(thread_summary)

    def export_metrics(self, filepath: str, duration_minutes: int = 60):
        """Export metrics ra file JSON"""
        samples_to_export = min(len(self.metrics_buffer), duration_minutes * 60)
        data_to_export = list(self.metrics_buffer)[-samples_to_export:]

        export_data = {
            'export_time': time.time(),
            'duration_minutes': duration_minutes,
            'sample_count': len(data_to_export),
            'metrics': data_to_export,
            'summary': self.get_thread_performance_summary()
        }

        with open(filepath, 'w') as f:
            json.dump(export_data, f, indent=2)

        print(f"[PerformanceTracker] Exported {len(data_to_export)} samples to {filepath}")

# Global instance
performance_tracker = PerformanceTracker.get_instance()
```

### 2. Context Manager cho Measuring Operations

```python
import time
from contextlib import contextmanager
from typing import Optional

class OperationMeasurer:
    """Context manager để đo thời gian thực hiện operations"""

    def __init__(self, tracker: PerformanceTracker):
        self.tracker = tracker
        self.operation_metrics = defaultdict(list)

    @contextmanager
    def measure_operation(self, operation_name: str):
        """Context manager để đo operation"""
        start_time = time.perf_counter()
        start_memory = psutil.Process().memory_info().rss

        try:
            yield
        finally:
            end_time = time.perf_counter()
            end_memory = psutil.Process().memory_info().rss

            duration = end_time - start_time
            memory_delta = end_memory - start_memory

            self.operation_metrics[operation_name].append({
                'duration': duration,
                'memory_delta': memory_delta,
                'timestamp': time.time()
            })

            # Keep only last 100 measurements per operation
            if len(self.operation_metrics[operation_name]) > 100:
                self.operation_metrics[operation_name] = \
                    self.operation_metrics[operation_name][-100:]

    def get_operation_stats(self, operation_name: str) -> Dict[str, float]:
        """Lấy thống kê của một operation"""
        measurements = self.operation_metrics.get(operation_name, [])
        if not measurements:
            return {}

        durations = [m['duration'] for m in measurements]
        memory_deltas = [m['memory_delta'] for m in measurements]

        return {
            'count': len(measurements),
            'avg_duration': sum(durations) / len(durations),
            'max_duration': max(durations),
            'min_duration': min(durations),
            'avg_memory_delta': sum(memory_deltas) / len(memory_deltas),
            'total_memory_allocated': sum(d for d in memory_deltas if d > 0)
        }

# Global operation measurer
operation_measurer = OperationMeasurer(performance_tracker)
```

## Kết luận

Hệ thống tracking này được thiết kế để cung cấp thông tin chi tiết về hiệu năng ứng dụng mà không ảnh hưởng đến trải nghiệm người dùng. Với việc sử dụng sampling thông minh và chạy trên thread riêng biệt, nó có thể giúp developers nhanh chóng xác định và giải quyết các vấn đề hiệu năng.

### Các tính năng chính:

1. **Non-intrusive monitoring** - Chạy trên thread riêng, không block main thread
2. **Thread-specific tracking** - Xác định chính xác thread nào consume resources nhiều nhất
3. **Adaptive sampling** - Tự động điều chỉnh tần suất sampling dựa trên tải hệ thống
4. **Real-time alerts** - Cảnh báo ngay khi có vấn đề hiệu năng
5. **Operation measurement** - Context manager để đo performance của specific operations
6. **Data export** - Xuất data để phân tích offline
7. **Memory leak detection** - Theo dõi Qt objects và memory usage patterns

### Cách sử dụng:

```python
# Khởi tạo trong main application
performance_tracker.enabled = True
performance_tracker.start_monitoring()

# Đo performance của operations
with operation_measurer.measure_operation("video_processing"):
    # Your video processing code here
    process_video_frame()

# Lấy thread performance summary
thread_summary = performance_tracker.get_thread_performance_summary()
print("Top CPU consuming threads:")
for thread_name, stats in sorted(thread_summary.items(),
                                key=lambda x: x[1]['avg_cpu_time'],
                                reverse=True)[:5]:
    print(f"  {thread_name}: {stats['avg_cpu_time']:.4f}s avg CPU time")
```
